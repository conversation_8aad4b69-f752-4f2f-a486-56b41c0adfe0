#!/usr/bin/env python3
"""
Simple Test for Dynamic Risk Scaling Logic
"""

def test_risk_scaling_logic():
    print('🔧 TESTING DYNAMIC RISK SCALING LOGIC')
    print('=' * 70)
    
    def calculate_risk(account_value):
        """Calculate risk based on account value using the compound scaling rules."""
        base_risk = 10.0
        compound_threshold = 1000.0
        compound_interval = 500.0
        risk_increase = 10.0
        
        if account_value >= compound_threshold:
            excess_above_threshold = account_value - compound_threshold
            intervals = int(excess_above_threshold // compound_interval)
            new_risk = base_risk + (intervals * risk_increase)
            
            # Ensure we maintain approximately 2% risk level
            max_risk_2_percent = account_value * 0.02
            new_risk = min(new_risk, max_risk_2_percent)
            
            return new_risk, intervals
        else:
            return base_risk, 0
    
    # Test scenarios
    test_cases = [
        (300, 10, 0, "Starting account"),
        (800, 10, 0, "Below compound threshold"),
        (1000, 10, 0, "At compound threshold"),
        (1500, 20, 1, "First compound interval ($1500)"),
        (2000, 30, 2, "Second compound interval ($2000)"),
        (2500, 40, 3, "Third compound interval ($2500)"),
        (3000, 50, 4, "Fourth compound interval ($3000)"),
        (5000, 90, 8, "Large account ($5000)"),  # 8 intervals = $10 + (8*$10) = $90
        (10000, 190, 18, "Very large account ($10000)"),  # 18 intervals = $10 + (18*$10) = $190
    ]
    
    print(f'📊 RISK SCALING TEST RESULTS:')
    print('=' * 70)
    
    all_passed = True
    
    for account_value, expected_risk, expected_intervals, description in test_cases:
        actual_risk, actual_intervals = calculate_risk(account_value)
        risk_percent = (actual_risk / account_value) * 100
        profit_target = actual_risk * 2.5  # 2.5:1 ratio
        
        # Check if test passed
        passed = (actual_risk == expected_risk and actual_intervals == expected_intervals)
        status = "✅ PASS" if passed else "❌ FAIL"
        
        if not passed:
            all_passed = False
        
        print(f'{description}:')
        print(f'   Account: ${account_value:,}')
        print(f'   Risk: ${actual_risk:.0f} (Expected: ${expected_risk:.0f})')
        print(f'   Profit Target: ${profit_target:.0f}')
        print(f'   Risk Level: {risk_percent:.2f}%')
        print(f'   Intervals: {actual_intervals} (Expected: {expected_intervals})')
        print(f'   Status: {status}')
        print()
    
    print(f'🎯 COMPOUND SCALING RULES:')
    print('=' * 70)
    print(f'✅ Base Risk: $10 (accounts below $1,000)')
    print(f'✅ Compound Threshold: $1,000')
    print(f'✅ Compound Interval: Every $500')
    print(f'✅ Risk Increase: $10 per interval')
    print(f'✅ Maximum Risk: 2% of account value')
    print(f'✅ Reward Ratio: 2.5:1 maintained')
    
    print(f'\n📈 PROGRESSION EXAMPLES:')
    print('=' * 70)
    progression_examples = [1000, 1500, 2000, 2500, 3000, 4000, 5000]
    
    for account in progression_examples:
        risk, intervals = calculate_risk(account)
        profit_target = risk * 2.5
        risk_percent = (risk / account) * 100
        print(f'   ${account:,} → Risk: ${risk:.0f}, Target: ${profit_target:.0f}, Level: {risk_percent:.2f}%')
    
    if all_passed:
        print(f'\n✅ ALL TESTS PASSED!')
        print(f'   Dynamic risk scaling logic is working correctly')
        print(f'   Ready for implementation in trading system')
    else:
        print(f'\n❌ SOME TESTS FAILED!')
        print(f'   Check the logic above')
    
    return all_passed

if __name__ == "__main__":
    test_risk_scaling_logic()
