#!/usr/bin/env python3
"""
Bitcoin Freedom Live Trading Web Application
===========================================

Live trading interface for the best composite ensemble model:
- Model: tcn_cnn_ppo_conservative_v3_20250601_234301 (Cycle 1)
- Composite Score: 91.4%
- Net Profit: $4,476.37
- Win Rate: 98.0%
- Trades/Day: 6.1
- Live Trading Ready: ✅

Prepared for Binance integration and real money trading.

Author: Trading System VPS 4
Date: 2025-01-27
"""

import os
import json
import time
import threading
import requests
import numpy as np
from datetime import datetime
from dataclasses import dataclass
from typing import List, Optional
from flask import Flask, render_template, jsonify, request

# Try to import intelligent margin manager
try:
    from intelligent_margin_manager import IntelligentMarginManager
    MARGIN_MANAGER_AVAILABLE = True
except ImportError:
    print("⚠️ Intelligent Margin Manager not available - running without advanced margin features")
    IntelligentMarginManager = None
    MARGIN_MANAGER_AVAILABLE = False

# Try to import intelligent portfolio rebalancer
try:
    from intelligent_portfolio_rebalancer import IntelligentPortfolioRebalancer
    PORTFOLIO_REBALANCER_AVAILABLE = True
except ImportError:
    print("⚠️ Intelligent Portfolio Rebalancer not available - running without auto-rebalancing")
    IntelligentPortfolioRebalancer = None
    PORTFOLIO_REBALANCER_AVAILABLE = False

# Try to import ccxt for live trading
try:
    import ccxt
    CCXT_AVAILABLE = True
    print("✅ CCXT available - Live trading enabled")
except ImportError:
    CCXT_AVAILABLE = False
    print("⚠️ CCXT not available - using Simple Binance Connector")

# Import Simple Binance Connector as fallback
try:
    from simple_binance_connector import SimpleBinanceConnector, create_simple_binance_connector
    SIMPLE_BINANCE_AVAILABLE = True
    print("✅ Simple Binance Connector available")
except ImportError:
    SIMPLE_BINANCE_AVAILABLE = False
    print("❌ Simple Binance Connector not available")

# Import CSV Trade Logger and Database
try:
    from trade_csv_logger import TradeCSVLogger
    from trade_database import TradingDatabase, get_trading_database
    CSV_LOGGER_AVAILABLE = True
    DATABASE_AVAILABLE = True
    print("✅ CSV Trade Logger available")
    print("✅ Trading Database available")

    # Initialize instances
    csv_logger = TradeCSVLogger("trade_history.csv")
    trading_db = get_trading_database()

except ImportError:
    CSV_LOGGER_AVAILABLE = False
    DATABASE_AVAILABLE = False
    csv_logger = None
    trading_db = None
    print("❌ CSV Trade Logger not available")
    print("❌ Trading Database not available")

# Import Robust Metrics Calculator
try:
    from robust_metrics_calculator import RobustMetricsCalculator
    ROBUST_METRICS_AVAILABLE = True
    print("✅ Robust Metrics Calculator available")

    # Initialize robust metrics calculator
    robust_calculator = RobustMetricsCalculator()

except ImportError:
    ROBUST_METRICS_AVAILABLE = False
    robust_calculator = None
    print("❌ Robust Metrics Calculator not available")

# Import Data Cache Manager for persistence
try:
    from data_cache_manager import DataCacheManager, get_cache_manager
    CACHE_MANAGER_AVAILABLE = True
    cache_manager = get_cache_manager()
    print("✅ Data Cache Manager available")
except ImportError as e:
    CACHE_MANAGER_AVAILABLE = False
    cache_manager = None
    print(f"⚠️ Data Cache Manager not available: {e}")

# Import AI Signal Monitor for confidence tracking
try:
    from ai_signal_monitor import AISignalMonitor, start_ai_monitoring, get_ai_status, get_ai_trend, get_recent_ai_signals
    AI_MONITOR_AVAILABLE = True
    print("✅ AI Signal Monitor available")

    # Initialize AI monitor
    ai_monitor = AISignalMonitor()

except ImportError as e:
    AI_MONITOR_AVAILABLE = False
    ai_monitor = None
    print(f"⚠️ AI Signal Monitor not available: {e}")

# Import TCN-CNN-PPO Ensemble System
try:
    # Try to import the full ensemble system first
    from enhanced_retraining_system_tcn_cnn_ppo import (
        TCNCNNPPOEnsemble,
        TCNEnsembleConfig,
        EnsemblePerformance
    )
    import torch
    TCN_CNN_PPO_AVAILABLE = True
    print("✅ TCN-CNN-PPO Ensemble available")

    # Initialize ensemble configuration
    ensemble_config = TCNEnsembleConfig()

except ImportError:
    # Fallback to mock model if full system not available
    try:
        from create_mock_tcn_cnn_ppo_model import MockTCNCNNPPOEnsemble
        import torch
        TCN_CNN_PPO_AVAILABLE = True
        print("✅ Mock TCN-CNN-PPO Ensemble available")

        # Create mock config
        class MockEnsembleConfig:
            sequence_length = 24
            num_features = 9
            tcn_weight = 0.40
            cnn_weight = 0.40
            ppo_weight = 0.20
            risk_per_trade = 10.0
            reward_ratio = 2.0

        ensemble_config = MockEnsembleConfig()

    except ImportError:
        TCN_CNN_PPO_AVAILABLE = False
        ensemble_config = None
        print("❌ TCN-CNN-PPO Ensemble not available")

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'tcn_cnn_ppo_live_trading_2025'

# Add CORS headers to all responses
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

class HighPerformanceModelManager:
    """Manager for high-performance TCN-CNN-PPO models with real-money trading capabilities."""

    def __init__(self):
        # Available high-performance models
        self.available_models = {
            'conservative_elite': {
                'id': 'tcn_cnn_ppo_conservative_v3_20250604_111817',
                'name': 'Conservative Elite (93.2% Win Rate)',
                'file': 'backup_tcn_cnn_ppo_composite_20250604_112552.pth',
                'composite_score': 0.791,
                'win_rate': 0.932,
                'net_profit': 3106.50,
                'trades_per_day': 5.8,
                'sharpe_ratio': 61.20,
                'max_drawdown': 0.038,
                'risk_level': 'conservative',
                'live_ready': True
            },
            'profit_maximizer': {
                'id': 'tcn_cnn_ppo_high_frequency_v1_20250604_111033',
                'name': 'Profit Maximizer ($5,151 Target)',
                'file': 'best_profit_tcn_cnn_ppo_20250604_112552.pth',
                'composite_score': 0.784,
                'win_rate': 0.824,
                'net_profit': 5151.38,
                'trades_per_day': 7.8,
                'sharpe_ratio': 66.23,
                'max_drawdown': 0.042,
                'risk_level': 'aggressive',
                'live_ready': True
            },
            'balanced_performer': {
                'id': 'tcn_cnn_ppo_balanced_v2_20250604_111431',
                'name': 'Balanced Performer',
                'file': 'tcn_cnn_ppo_ensemble_simplified_20250603_191323.pth',
                'composite_score': 0.756,
                'win_rate': 0.798,
                'net_profit': 4200.00,
                'trades_per_day': 6.5,
                'sharpe_ratio': 58.45,
                'max_drawdown': 0.035,
                'risk_level': 'balanced',
                'live_ready': True
            }
        }

        # Current active model
        self.active_model_key = 'conservative_elite'  # Default to safest model
        self.ai_ensemble = None
        self.model_cache = {}  # Cache loaded models
        self.ai_scaler = None

        # Load default model
        self.load_active_model()

    def get_active_model(self):
        """Get current active model configuration."""
        return self.available_models[self.active_model_key]

    def switch_model(self, model_key: str) -> bool:
        """Switch to a different high-performance model."""
        if model_key not in self.available_models:
            print(f"❌ Model {model_key} not available")
            return False

        print(f"🔄 Switching from {self.active_model_key} to {model_key}")
        self.active_model_key = model_key
        return self.load_active_model()

    def load_active_model(self) -> bool:
        """Load the currently active model."""
        model_config = self.available_models[self.active_model_key]

        # Check cache first
        if self.active_model_key in self.model_cache:
            self.ai_ensemble = self.model_cache[self.active_model_key]
            print(f"✅ Loaded cached model: {model_config['name']}")
            return True

        # Always load the default model first to ensure consistent state
        self.load_default_model()

        # Try to load advanced models but don't override default if they fail
        try:
            if self.load_tcn_cnn_ppo_model():
                print("🤖 TCN-CNN-PPO AI Ensemble Model Loaded - SIMULATION MODE READY")
            else:
                print("⚠️ TCN-CNN-PPO not available, loading focused model as fallback")
                self.load_focused_model()
        except Exception as e:
            print(f"⚠️ Advanced model loading failed: {e}")
            print("✅ Using default model configuration")

        print(f"✅ Loaded Model: {self.model_id}")
        print(f"   Composite Score: {self.composite_score:.1%}")
        print(f"   Expected Trades/Day: {self.trades_per_day}")
        print(f"   Win Rate: {self.win_rate:.1%}")

        return True

    def load_tcn_cnn_ppo_model(self):
        """Load the trained TCN-CNN-PPO ensemble model if available."""
        if not TCN_CNN_PPO_AVAILABLE:
            return False

        try:
            # Look for the latest trained ensemble model (prioritize simplified)
            models_dir = "models"
            ensemble_files = []

            if os.path.exists(models_dir):
                for file in os.listdir(models_dir):
                    if (file.startswith("tcn_cnn_ppo_ensemble_") and file.endswith(".pth")):
                        ensemble_files.append(file)

            if not ensemble_files:
                print("⚠️ No trained TCN-CNN-PPO ensemble models found")
                return False

            # Prioritize simplified model, then most recent
            simplified_models = [f for f in ensemble_files if "simplified" in f]
            if simplified_models:
                latest_model = sorted(simplified_models)[-1]
            else:
                latest_model = sorted(ensemble_files)[-1]

            model_path = os.path.join(models_dir, latest_model)

            print(f"🤖 Loading TCN-CNN-PPO ensemble: {latest_model}")

            # Initialize ensemble model (try simplified first, then full, then mock)
            try:
                if "simplified" in latest_model:
                    from simplified_tcn_cnn_ppo_trainer import SimplifiedTCNCNNPPOEnsemble
                    self.ai_ensemble = SimplifiedTCNCNNPPOEnsemble(24, 9)
                else:
                    self.ai_ensemble = TCNCNNPPOEnsemble(ensemble_config, target_frequency='balanced')
            except Exception as e:
                print(f"⚠️ Error initializing ensemble: {e}")
                # Fallback to mock model
                from create_mock_tcn_cnn_ppo_model import MockTCNCNNPPOEnsemble
                self.ai_ensemble = MockTCNCNNPPOEnsemble()

            # Load trained weights
            checkpoint = torch.load(model_path, map_location='cpu')
            self.ai_ensemble.load_state_dict(checkpoint['model_state_dict'])
            self.ai_ensemble.eval()

            # Load scaler if available
            scaler_path = model_path.replace('.pth', '_scaler.pkl')
            if os.path.exists(scaler_path):
                import joblib
                self.ai_scaler = joblib.load(scaler_path)

            # Load model metadata
            metadata_path = model_path.replace('.pth', '_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)

                # Only override default model properties if loading is successful
                # Store in temporary variables first
                temp_model_id = metadata.get('model_id', latest_model.replace('.pth', ''))
                temp_model_type = "TCN-CNN-PPO Ensemble AI"
                temp_cycle = metadata.get('cycle', 3)
                temp_composite_score = metadata.get('composite_score', 0.91)
                temp_win_rate = metadata.get('win_rate', 0.78)

                # Only set properties if we can successfully access them
                self._model_id = temp_model_id
                self._model_type = temp_model_type
                self.cycle = temp_cycle
                self._composite_score = temp_composite_score
                self._win_rate = temp_win_rate
                self._trades_per_day = "UNLIMITED"  # AI-driven, no limits
                self.profit_factor = metadata.get('profit_factor', 5.2)
                self._max_drawdown = metadata.get('max_drawdown', 0.025)
                self._sharpe_ratio = metadata.get('sharpe_ratio', 65.0)

                # AI ensemble weights
                self.tcn_weight = 0.40  # 40%
                self.cnn_weight = 0.40  # 40%
                self.ppo_weight = 0.20  # 20%

                # Trading parameters
                self.account_size = 300.0
                self.risk_per_trade = 10.0
                self.reward_ratio = 2.5
                self.profit_target = 25.0
                self.target_frequency = "ai_driven"
                self.live_trading_ready = True

                # Grid trading parameters
                self.margin_type = "CROSS"
                self.grid_size_percent = 0.0025  # LOCKED: 0.25% grid spacing
                self.leverage = 1.0
                self.min_price_movement = 0.0025

                print(f"🎯 Loaded TCN-CNN-PPO AI Ensemble Model")
                print(f"   TCN Weight: {self.tcn_weight:.1%}")
                print(f"   CNN Weight: {self.cnn_weight:.1%}")
                print(f"   PPO Weight: {self.ppo_weight:.1%}")
                return True

        except Exception as e:
            print(f"⚠️ Error loading TCN-CNN-PPO model: {e}")
            # Don't modify any properties if loading fails
            return False

        return False

    def load_focused_model(self):
        """Load the focused 4-indicator model if available."""
        try:
            metadata_path = os.path.join("models", "webapp_focused_model_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)

                # Load from focused model metadata
                self.model_id = metadata['model_id']
                self.model_type = "Focused 4-Indicator Model"
                self.cycle = 2  # New focused model cycle

                # Performance metrics from focused model - USING ROBUST SCORING
                self.net_profit = 5200.0  # Projected higher profit

                # Use robust score if available, fallback to metadata composite score
                if ROBUST_METRICS_AVAILABLE:
                    # Calculate robust score from model performance
                    self.robust_score = metadata.get('robust_score', 0.892)  # Default 89.2%
                    self.composite_score = self.robust_score  # Use robust score as composite
                else:
                    self.composite_score = metadata['composite_score']  # Fallback to 89.2%

                self.win_rate = metadata['win_rate']  # 84.7%
                self.trades_per_day = metadata['trades_per_day']  # 6.8 - NO LIMITS
                self.profit_factor = 4.8  # Improved profit factor
                self.max_drawdown = 0.035  # 3.5% - Better risk management
                self.sharpe_ratio = 58.4  # Improved Sharpe ratio

                # Component weights from focused model (4 indicators)
                weights = metadata.get('indicator_weights', {})
                self.tcn_weight = weights.get('vwap', 0.28)  # VWAP weight
                self.cnn_weight = weights.get('bollinger_bands', 0.31)  # Bollinger Bands weight
                self.ppo_weight = weights.get('flow_strength', 0.22)  # Flow Strength weight
                self.eth_btc_weight = weights.get('eth_btc_ratio', 0.19)  # ETH/BTC Ratio weight

                # Trading parameters from focused model - UPDATED TO 2:1 RATIO
                self.account_size = 300.0
                self.risk_per_trade = metadata['risk_per_trade']  # $10
                self.reward_ratio = 2.0  # 2:1 ratio for comparison testing
                self.profit_target = self.risk_per_trade * self.reward_ratio  # $20 (2:1 ratio)
                self.target_frequency = "active"  # NO ARTIFICIAL LIMITS
                self.live_trading_ready = True

                # Cross Margin & Grid Trading Parameters - LOCKED SETTINGS
                self.margin_type = "CROSS"
                self.grid_size_percent = 0.0025  # LOCKED: 0.25% grid spacing - DO NOT CHANGE
                self.leverage = 1.0
                self.min_price_movement = 0.0025  # LOCKED: 0.25% minimum movement

                print(f"🎯 Loaded Focused Model with 4 Key Indicators - NO TRADE LIMITS")
                return

        except Exception as e:
            print(f"⚠️ Could not load focused model: {e}")

        # Fallback to default model
        self.load_default_model()



    def load_default_model(self):
        """Load default model configuration."""
        # Set basic model attributes
        self._model_id = "tcn_cnn_ppo_conservative_v3_20250604_111817"  # Best composite model from retraining
        self._model_type = "TCN-CNN-PPO Conservative Elite (4 Indicators)"
        self.cycle = 2  # Updated cycle after retraining

        # Performance metrics - Latest TCN-CNN-PPO Retraining Results (4 Indicators Only)
        self._net_profit = 5151.38   # Best profit model result
        self._composite_score = 0.791  # 79.1% - Conservative model (best composite)
        self._win_rate = 0.932  # 93.2% Win Rate (Conservative model)
        self._trades_per_day = 5.8  # Conservative trading frequency
        self.profit_factor = 4.2
        self._max_drawdown = 0.04  # 4% - Excellent Risk Management
        self._sharpe_ratio = 61.20  # Updated from retraining

        # Component weights - Optimized Balance
        self.tcn_weight = 0.398  # 39.8% TCN
        self.cnn_weight = 0.439  # 43.9% CNN
        self.ppo_weight = 0.164  # 16.4% PPO

        # Trading parameters - $300 Cross Margin Account
        self.account_size = 300.0
        self.risk_per_trade = 10.0  # $10 risk
        self.reward_ratio = 2.5     # 2.5:1 ratio
        self.profit_target = 25.0   # $25 profit target
        self.target_frequency = "conservative"
        self.live_trading_ready = True

        # Cross Margin & Grid Trading Parameters - LOCKED SETTINGS
        self.margin_type = "CROSS"    # Cross margin account
        self.grid_size_percent = 0.0025  # LOCKED: 0.25% grid spacing - DO NOT CHANGE
        self.leverage = 1.0         # 1x leverage (conservative)
        self.min_price_movement = 0.0025  # LOCKED: 0.25% minimum movement

    @property
    def model_id(self):
        """Get current model ID."""
        if hasattr(self, '_model_id'):
            return self._model_id
        return self.get_active_model()['id']

    @property
    def model_type(self):
        """Get current model type."""
        if hasattr(self, '_model_type'):
            return self._model_type
        return self.get_active_model()['name']

    @property
    def composite_score(self):
        """Get current composite score."""
        if hasattr(self, '_composite_score'):
            return self._composite_score
        return self.get_active_model()['composite_score']

    @property
    def win_rate(self):
        """Get current win rate."""
        if hasattr(self, '_win_rate'):
            return self._win_rate
        return self.get_active_model()['win_rate']

    @property
    def net_profit(self):
        """Get current net profit target."""
        if hasattr(self, '_net_profit'):
            return self._net_profit
        return self.get_active_model()['net_profit']

    @property
    def trades_per_day(self):
        """Get current trades per day."""
        if hasattr(self, '_trades_per_day'):
            return self._trades_per_day
        return self.get_active_model()['trades_per_day']

    @property
    def sharpe_ratio(self):
        """Get current Sharpe ratio."""
        if hasattr(self, '_sharpe_ratio'):
            return self._sharpe_ratio
        return self.get_active_model()['sharpe_ratio']

    @property
    def max_drawdown(self):
        """Get current max drawdown."""
        if hasattr(self, '_max_drawdown'):
            return self._max_drawdown
        return self.get_active_model()['max_drawdown']

# Compatibility alias for existing code
BestCompositeModel = HighPerformanceModelManager

class CrossMarginCalculator:
    """Cross margin position sizing and grid trading calculator."""

    def __init__(self, model: BestCompositeModel):
        self.model = model
        # Validate and enforce locked grid spacing
        self._validate_grid_spacing()

    def _validate_grid_spacing(self):
        """Validate that grid spacing is locked at 0.25% and cannot be changed."""
        REQUIRED_GRID_SPACING = 0.0025  # 0.25% - LOCKED VALUE

        if hasattr(self.model, 'grid_size_percent'):
            if abs(self.model.grid_size_percent - REQUIRED_GRID_SPACING) > 0.0001:
                print(f"⚠️ WARNING: Grid spacing was {self.model.grid_size_percent:.4f}, forcing to LOCKED value {REQUIRED_GRID_SPACING}")
                self.model.grid_size_percent = REQUIRED_GRID_SPACING
        else:
            self.model.grid_size_percent = REQUIRED_GRID_SPACING

        print(f"🔒 Grid spacing LOCKED at {REQUIRED_GRID_SPACING:.4f} (0.25%)")

    def calculate_position_size(self, entry_price: float, risk_amount: float) -> dict:
        """Calculate accurate position size for cross margin account - LOCKED 0.25% GRID."""

        # LOCKED GRID SPACING - DO NOT CHANGE
        LOCKED_GRID_SPACING = 0.0025  # 0.25% - NEVER MODIFY THIS VALUE
        grid_size_usd = entry_price * LOCKED_GRID_SPACING

        # Stop loss distance (should be multiple of grid size for accuracy)
        stop_distance_grids = max(1, round(risk_amount / grid_size_usd))
        actual_stop_distance = stop_distance_grids * grid_size_usd

        # Target distance (2.5:1 ratio)
        target_distance_grids = stop_distance_grids * self.model.reward_ratio
        actual_target_distance = target_distance_grids * grid_size_usd

        # Position size calculation for cross margin
        # Position size = Risk Amount / Stop Distance
        position_size_usd = risk_amount / (actual_stop_distance / entry_price)
        position_size_btc = position_size_usd / entry_price

        # Ensure minimum position size (Binance minimum ~$5)
        min_position_usd = 5.0
        if position_size_usd < min_position_usd:
            position_size_usd = min_position_usd
            position_size_btc = position_size_usd / entry_price
            # Recalculate actual risk
            actual_risk = (actual_stop_distance / entry_price) * position_size_usd
        else:
            actual_risk = risk_amount

        return {
            'position_size_btc': round(position_size_btc, 6),
            'position_size_usd': round(position_size_usd, 2),
            'stop_distance_usd': round(actual_stop_distance, 2),
            'target_distance_usd': round(actual_target_distance, 2),
            'stop_distance_grids': stop_distance_grids,
            'target_distance_grids': target_distance_grids,
            'grid_size_usd': round(grid_size_usd, 2),
            'actual_risk': round(actual_risk, 2),
            'expected_profit': round(actual_target_distance * position_size_usd / entry_price, 2)
        }

    def calculate_grid_levels(self, entry_price: float, direction: str) -> dict:
        """Calculate grid trading levels for cross margin - LOCKED 0.25% GRID SPACING."""

        # LOCKED GRID SPACING - DO NOT CHANGE
        LOCKED_GRID_SPACING = 0.0025  # 0.25% - NEVER MODIFY THIS VALUE
        grid_size_usd = entry_price * LOCKED_GRID_SPACING

        if direction == 'BUY':
            # BUY position: profit when price goes up
            stop_loss = entry_price - (self.model.risk_per_trade / 0.001)  # Simplified for now
            take_profit = entry_price + (self.model.profit_target / 0.001)  # Simplified for now

            # Grid levels below entry (for averaging down if needed)
            grid_levels_down = [entry_price - (i * grid_size_usd) for i in range(1, 6)]
            # Grid levels above entry (for taking profits)
            grid_levels_up = [entry_price + (i * grid_size_usd) for i in range(1, 11)]

        else:  # SELL
            # SELL position: profit when price goes down
            stop_loss = entry_price + (self.model.risk_per_trade / 0.001)  # Simplified for now
            take_profit = entry_price - (self.model.profit_target / 0.001)  # Simplified for now

            # Grid levels above entry (for averaging down if needed)
            grid_levels_up = [entry_price + (i * grid_size_usd) for i in range(1, 6)]
            # Grid levels below entry (for taking profits)
            grid_levels_down = [entry_price - (i * grid_size_usd) for i in range(1, 11)]

        return {
            'entry_price': round(entry_price, 2),
            'stop_loss': round(stop_loss, 2),
            'take_profit': round(take_profit, 2),
            'grid_size_usd': round(grid_size_usd, 2),
            'grid_levels_up': [round(level, 2) for level in grid_levels_up],
            'grid_levels_down': [round(level, 2) for level in grid_levels_down]
        }

class RealTimePriceFetcher:
    """Fetches real-time BTC price data from multiple sources."""

    def __init__(self):
        self.last_price = 105000.0  # Current BTC price level
        self.last_update = datetime.now()
        self.price_sources = [
            "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT",
            "https://api.coinbase.com/v2/exchange-rates?currency=BTC",
            "https://api.kraken.com/0/public/Ticker?pair=XBTUSD"
        ]

    def get_real_btc_price(self) -> float:
        """Get real-time BTC price from live APIs."""

        # Try Binance first (most reliable)
        try:
            response = requests.get(
                "https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT",
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                price = float(data['price'])
                self.last_price = price
                self.last_update = datetime.now()
                print(f"📊 Real BTC Price (Binance): ${price:,.2f}")
                return price
        except Exception as e:
            print(f"⚠️ Binance API error: {e}")

        # Try Coinbase as backup
        try:
            response = requests.get(
                "https://api.coinbase.com/v2/exchange-rates?currency=BTC",
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                price = float(data['data']['rates']['USD'])
                self.last_price = price
                self.last_update = datetime.now()
                print(f"📊 Real BTC Price (Coinbase): ${price:,.2f}")
                return price
        except Exception as e:
            print(f"⚠️ Coinbase API error: {e}")

        # If all APIs fail, use last known price with small variation
        print(f"⚠️ Using last known price: ${self.last_price:,.2f}")
        return self.last_price

    def is_price_stale(self) -> bool:
        """Check if price data is stale (older than 30 seconds)."""
        return (datetime.now() - self.last_update).total_seconds() > 30

@dataclass
class LiveTrade:
    """Live trading position for Binance integration."""
    
    trade_id: str
    timestamp: datetime
    direction: str  # 'BUY' or 'SELL'
    entry_price: float
    quantity: float
    risk_amount: float
    target_profit: float
    stop_loss: float
    status: str  # 'OPEN', 'CLOSED_PROFIT', 'CLOSED_LOSS'
    exit_price: Optional[float] = None
    exit_timestamp: Optional[datetime] = None
    pnl: float = 0.0
    confidence: float = 0.0
    
    # Binance integration fields
    binance_order_id: Optional[str] = None
    binance_symbol: str = "BTCUSDT"
    order_type: str = "MARKET"

class LiveTradingEngine:
    """Live trading engine for the best composite model - Binance ready."""
    
    def __init__(self, model: BestCompositeModel):
        self.model = model
        self.is_running = False
        self.is_live_mode = False  # Toggle for live vs simulation

        # Real-time price fetcher and cross margin calculator
        self.price_fetcher = RealTimePriceFetcher()
        self.margin_calculator = CrossMarginCalculator(model)
        self.margin_manager = None  # Will be initialized when live mode is enabled
        self.portfolio_rebalancer = None  # Will be initialized when cross margin is enabled

        # Market data - Initialize with real BTC price
        self.current_price = self.price_fetcher.get_real_btc_price()  # Real BTC price
        self.price_history = []
        
        # Account management
        self.balance = model.account_size  # $300 starting balance
        self.equity = model.account_size
        self.available_balance = model.account_size
        
        # Trading state
        self.open_trades: List[LiveTrade] = []
        self.closed_trades: List[LiveTrade] = []
        self.daily_trades = 0
        self.last_trade_time = datetime.now()
        
        # Performance tracking
        self.total_profit = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.max_equity = model.account_size
        self.max_drawdown_value = 0.0
        
        # Risk management - COMPLETELY UNLIMITED TRADING
        # REMOVED: max_daily_trades - NO DAILY TRADE LIMITS AT ALL
        self.max_open_positions = 1   # ONLY ONE TRADE AT A TIME - Conservative focused approach
        self.daily_loss_limit = 100.0  # Daily loss limit for safety only
        # REMOVED: daily_profit_target - no artificial profit stopping
        # REMOVED: max_daily_trades - UNLIMITED TRADING FREQUENCY
        
        print(f"🚀 Live Trading Engine Initialized")
        print(f"   Model: {model.model_id}")
        print(f"   Composite Score: {model.composite_score:.1%}")
        print(f"   Account Size: ${model.account_size}")
        print(f"   Risk per Trade: ${model.risk_per_trade}")
        print(f"   Profit Target: ${model.profit_target}")
    
    def update_market_price(self):
        """Update market price with real BTC data - NO DUMMY DATA."""

        # Always use real BTC price data
        try:
            # Get real-time BTC price
            real_price = self.price_fetcher.get_real_btc_price()

            # Update current price with real data
            self.current_price = real_price

            print(f"💰 Real BTC Price Updated: ${self.current_price:,.2f}")

        except Exception as e:
            print(f"⚠️ Price update error: {e}")
            # If real price fails, keep last known real price (no dummy data)
            print(f"   Using last real price: ${self.current_price:,.2f}")

        # Store real price history
        self.price_history.append({
            'timestamp': datetime.now(),
            'price': self.current_price,
            'source': 'real_api'  # Mark as real data
        })

        # Keep only last 1000 real prices
        if len(self.price_history) > 1000:
            self.price_history = self.price_history[-1000:]

    def get_current_btc_price(self) -> float:
        """Get current BTC price with caching for outage resilience."""
        # Cache the current price for persistence
        if CACHE_MANAGER_AVAILABLE and self.current_price:
            cache_manager.cache_price_data('BTCUSDT', self.current_price, 'live_engine')

        # Return current price or fallback to cached if needed
        if self.current_price:
            return self.current_price
        elif CACHE_MANAGER_AVAILABLE:
            cached_price = cache_manager.get_latest_price('BTCUSDT')
            if cached_price:
                print(f"⚠️ Using cached BTC price: ${cached_price:,.2f}")
                return cached_price

        return self.current_price
    
    def should_enter_trade(self):
        """Determine if model should enter a new trade using GRID-BASED logic."""
        # Only essential risk management checks
        if len(self.open_trades) >= self.max_open_positions:
            return False

        # Daily loss limit check (safety only)
        daily_pnl = sum(t.pnl for t in self.closed_trades
                       if t.exit_timestamp and t.exit_timestamp.date() == datetime.now().date())
        if daily_pnl <= -self.daily_loss_limit:
            return False

        # GRID-BASED TRADING LOGIC
        return self.check_grid_trading_signals()

    def check_grid_trading_signals(self):
        """Check if current price has hit any grid levels that should trigger trades."""
        current_price = self.get_current_btc_price()
        if current_price is None:
            return False

        # Get grid spacing (0.25% locked)
        grid_size_usd = current_price * self.model.grid_size_percent  # 0.25% = $265 at $106k

        # Check if we have any recent price history to establish grid levels
        if len(self.price_history) < 2:
            # No price history - wait for price data to accumulate
            # REMOVED: Random probability trading - only trade on real signals
            return False

        # Get recent price movement
        previous_price = self.price_history[-2] if len(self.price_history) >= 2 else current_price
        price_change = current_price - previous_price
        price_change_percent = abs(price_change) / previous_price

        # Check if price has moved enough to trigger grid trading (0.25% minimum)
        if price_change_percent >= self.model.grid_size_percent:
            # Price has moved a full grid level - this should trigger a trade

            # Determine direction based on price movement
            if price_change > 0:
                # Price went up - consider SELL to take profit or BUY for momentum
                self.grid_signal_direction = 'SELL'  # Take profit on upward movement
                self.grid_signal_reason = f"Price up {price_change_percent:.2%} (>{self.model.grid_size_percent:.2%})"
            else:
                # Price went down - consider BUY to average down or SELL for momentum
                self.grid_signal_direction = 'BUY'   # Buy the dip on downward movement
                self.grid_signal_reason = f"Price down {price_change_percent:.2%} (>{self.model.grid_size_percent:.2%})"

            print(f"🔲 GRID SIGNAL: {self.grid_signal_direction} - {self.grid_signal_reason}")
            print(f"   Current: ${current_price:,.2f} | Previous: ${previous_price:,.2f}")
            print(f"   Grid Size: ${grid_size_usd:.2f} (0.25%)")

            return True

        # Check for AI Signal Monitor signals (NEW: 75% confidence threshold)
        if AI_MONITOR_AVAILABLE and ai_monitor:
            ai_signal_triggered = self.check_ai_monitor_signals()
            if ai_signal_triggered:
                return True

        # Check for AI-enhanced grid signals (if AI available)
        if self.model.ai_ensemble is not None:
            # Use AI to determine if we should trade even without full grid movement
            ai_signal = self.ai_trading_decision()
            if ai_signal:
                self.grid_signal_reason = "AI-enhanced grid signal"
                return True

        return False

    def check_ai_monitor_signals(self):
        """Check for AI Signal Monitor signals that exceed 75% confidence threshold."""
        try:
            # Get recent AI signals from the monitor
            recent_signals = ai_monitor.get_recent_signals(5)  # Last 5 signals

            if not recent_signals:
                return False

            # Check the most recent signal
            latest_signal = recent_signals[-1]

            # Check if signal meets all criteria for trade execution
            signal_confidence = latest_signal['confidence']
            action_probability = latest_signal['action_probability']
            action = latest_signal['action']
            signal_time = latest_signal['timestamp']

            # Signal must be recent (within last 30 seconds)
            time_since_signal = (datetime.now() - signal_time).total_seconds()
            if time_since_signal > 30:
                return False

            # Signal must meet confidence and probability thresholds
            if (signal_confidence >= 0.75 and  # 75% confidence threshold
                action_probability > 0.4 and   # 40% action probability threshold
                action in ['BUY', 'SELL']):    # Must be actionable signal

                # Store signal for trade execution
                self.grid_signal_direction = action
                self.grid_signal_reason = f"AI Monitor Signal: {signal_confidence:.1%} confidence"

                print(f"🎯 AI MONITOR SIGNAL TRIGGERED!")
                print(f"   Action: {action}")
                print(f"   Confidence: {signal_confidence:.1%} (threshold: 75%)")
                print(f"   Action Probability: {action_probability:.1%} (threshold: 40%)")
                print(f"   Signal Age: {time_since_signal:.1f}s")
                print(f"   Price: ${latest_signal['price']:,.2f}")

                return True
            else:
                # Log why signal didn't trigger
                reasons = []
                if signal_confidence < 0.75:
                    reasons.append(f"confidence {signal_confidence:.1%} < 75%")
                if action_probability <= 0.4:
                    reasons.append(f"action probability {action_probability:.1%} <= 40%")
                if action not in ['BUY', 'SELL']:
                    reasons.append(f"action '{action}' not actionable")

                print(f"⏳ AI signal not triggered: {', '.join(reasons)}")
                return False

        except Exception as e:
            print(f"⚠️ Error checking AI monitor signals: {e}")
            return False

    def ai_trading_decision(self):
        """Use TCN-CNN-PPO ensemble to make intelligent trading decisions."""
        try:
            # Get recent market data for AI prediction
            market_data = self.prepare_ai_input_data()
            if market_data is None:
                return False

            # Make prediction using AI ensemble
            with torch.no_grad():
                # Convert to tensor
                input_tensor = torch.FloatTensor(market_data).unsqueeze(0)  # Add batch dimension

                # Get ensemble prediction
                logits, frequency, risk_level, confidence, value = self.model.ai_ensemble(input_tensor)

                # Convert logits to probabilities
                probabilities = torch.softmax(logits, dim=-1)
                predicted_action = torch.argmax(probabilities, dim=-1).item()

                # Get confidence and frequency scores
                confidence_score = confidence.item()
                frequency_score = frequency.item()

                # AI decision logic
                # 0 = HOLD, 1 = BUY, 2 = SELL
                if predicted_action == 0:  # HOLD
                    return False

                # Check confidence threshold
                if confidence_score < 0.6:  # Require 60% confidence
                    return False

                # AI-driven trading decision based on confidence and frequency
                # REMOVED: Random probability - use pure AI decision
                confidence_threshold = 0.6  # 60% minimum confidence
                frequency_threshold = 0.4   # 40% minimum frequency score

                should_trade = (confidence_score >= confidence_threshold and
                               frequency_score >= frequency_threshold)

                if should_trade:
                    # Store the predicted direction for use in execute_trade
                    self.ai_predicted_direction = 'BUY' if predicted_action == 1 else 'SELL'
                    self.ai_confidence = confidence_score
                    print(f"🤖 AI Decision: {self.ai_predicted_direction} (Confidence: {confidence_score:.1%})")

                return should_trade

        except Exception as e:
            print(f"⚠️ AI trading decision error: {e}")
            # NO FALLBACK TO RANDOM - Only trade on real signals
            return False

    def prepare_ai_input_data(self):
        """Prepare REAL market data using ONLY 4 specified indicators - NO OTHER INDICATORS."""
        try:
            # Get current price
            current_price = self.get_current_btc_price()
            if current_price is None:
                return None

            # Check if we have enough real price history
            sequence_length = 24  # 24 data points needed
            if len(self.price_history) < sequence_length:
                print(f"⚠️ Insufficient real price history: {len(self.price_history)}/{sequence_length} - AI disabled")
                return None

            # Use REAL price history from our price_history list
            recent_prices = [entry['price'] for entry in self.price_history[-sequence_length:]]

            # Calculate ONLY the 4 specified indicators
            market_sequence = []

            # Calculate indicators over the sequence
            for i in range(len(recent_prices)):
                price = recent_prices[i]

                # 1. VWAP (20-period) - Volume Weighted Average Price
                if i < 20:
                    vwap = price  # Use current price for insufficient data
                else:
                    # Simple VWAP approximation using price and estimated volume
                    prices_20 = recent_prices[i-19:i+1]
                    volumes_20 = [1000 * (1 + abs((p - recent_prices[max(0, j-1)]) / recent_prices[max(0, j-1)]) * 10)
                                 for j, p in enumerate(prices_20)]
                    total_pv = sum(p * v for p, v in zip(prices_20, volumes_20))
                    total_v = sum(volumes_20)
                    vwap = total_pv / total_v if total_v > 0 else price

                # 2. RSI (5-period) - Fast momentum oscillator
                if i < 5:
                    rsi = 50  # Neutral RSI for insufficient data
                else:
                    # Calculate 5-period RSI
                    prices_5 = recent_prices[i-4:i+1]
                    gains = []
                    losses = []
                    for j in range(1, len(prices_5)):
                        change = prices_5[j] - prices_5[j-1]
                        if change > 0:
                            gains.append(change)
                            losses.append(0)
                        else:
                            gains.append(0)
                            losses.append(abs(change))

                    avg_gain = sum(gains) / len(gains) if gains else 0
                    avg_loss = sum(losses) / len(losses) if losses else 0

                    if avg_loss == 0:
                        rsi = 100
                    else:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))

                # 3. Bollinger Bands Position (20-period, 2 std dev)
                if i < 20:
                    bb_position = 0.5  # Middle position for insufficient data
                else:
                    # Calculate 20-period Bollinger Bands
                    prices_20 = recent_prices[i-19:i+1]
                    sma_20 = sum(prices_20) / len(prices_20)
                    variance = sum((p - sma_20) ** 2 for p in prices_20) / len(prices_20)
                    std_dev = variance ** 0.5

                    upper_band = sma_20 + (2 * std_dev)
                    lower_band = sma_20 - (2 * std_dev)

                    # Position within bands (0 = lower band, 0.5 = middle, 1 = upper band)
                    if upper_band == lower_band:
                        bb_position = 0.5
                    else:
                        bb_position = (price - lower_band) / (upper_band - lower_band)
                        bb_position = max(0, min(1, bb_position))  # Clamp 0-1

                # 4. ETH/BTC Ratio - Market sentiment indicator
                # Simplified approximation: use price momentum as proxy for market sentiment
                if i < 5:
                    eth_btc_ratio = 0.05  # Default ratio
                else:
                    # Use recent price momentum as proxy for ETH/BTC sentiment
                    recent_momentum = (price - recent_prices[i-5]) / recent_prices[i-5]
                    # Convert momentum to ratio-like value around 0.05
                    eth_btc_ratio = 0.05 + (recent_momentum * 0.01)  # Scale momentum
                    eth_btc_ratio = max(0.03, min(0.07, eth_btc_ratio))  # Clamp realistic range

                # ONLY these 4 indicators - NO OTHER FEATURES
                features = [
                    vwap,           # 1. VWAP (20-period)
                    rsi,            # 2. RSI (5-period)
                    bb_position,    # 3. Bollinger Bands Position
                    eth_btc_ratio   # 4. ETH/BTC Ratio
                ]

                market_sequence.append(features)

            # Convert to numpy array
            market_data = np.array(market_sequence, dtype=np.float32)

            # Apply scaling if scaler is available
            if self.model.ai_scaler is not None:
                # Reshape for scaling
                original_shape = market_data.shape
                market_data_flat = market_data.reshape(-1, market_data.shape[-1])
                market_data_scaled = self.model.ai_scaler.transform(market_data_flat)
                market_data = market_data_scaled.reshape(original_shape)

            print(f"✅ AI input prepared with ONLY 4 indicators: VWAP, RSI(5), BB Position, ETH/BTC Ratio")
            print(f"   Data points: {len(recent_prices)} | Features per point: 4")
            return market_data

        except Exception as e:
            print(f"⚠️ Error preparing 4-indicator AI input data: {e}")
            return None
    
    def generate_trade_signal(self):
        """Generate trading signal using GRID-BASED logic and AI ensemble."""
        # Use grid-based direction if available
        if hasattr(self, 'grid_signal_direction') and hasattr(self, 'grid_signal_reason'):
            direction = self.grid_signal_direction
            reason = self.grid_signal_reason

            # Grid-based signals have high confidence
            if "AI-enhanced" in reason:
                confidence = 0.8  # 80% confidence for AI-enhanced grid signals
            else:
                confidence = 0.9  # 90% confidence for pure grid signals

            print(f"🔲 GRID TRADE SIGNAL: {direction} | Confidence: {confidence:.1%} | Reason: {reason}")

            # Clear the stored signal
            delattr(self, 'grid_signal_direction')
            delattr(self, 'grid_signal_reason')
            return direction, confidence

        # Use AI prediction if available (fallback)
        if hasattr(self, 'ai_predicted_direction') and hasattr(self, 'ai_confidence'):
            direction = self.ai_predicted_direction
            confidence = self.ai_confidence
            print(f"🤖 AI TRADE SIGNAL: {direction} | Confidence: {confidence:.1%}")
            # Clear the stored prediction
            delattr(self, 'ai_predicted_direction')
            delattr(self, 'ai_confidence')
            return direction, confidence

        # No valid signal available
        print("⚠️ No valid trading signal available (waiting for grid movement or AI signal)")
        return None, 0.0
    
    def enter_trade(self, direction: str, confidence: float):
        """Enter a new trading position with cross margin calculations."""
        trade_id = f"LIVE_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.closed_trades) + len(self.open_trades) + 1:03d}"

        # Entry price - use current market price (no random slippage)
        # In live trading, slippage will be real market slippage
        entry_price = self.current_price

        # Initialize position_calc with default calculator
        position_calc = self.margin_calculator.calculate_position_size(
            entry_price, self.model.risk_per_trade
        )

        # Use intelligent margin manager if available (cross margin mode)
        if self.margin_manager and self.is_live_mode:
            # Get current margin status
            margin_status = self.margin_manager.get_margin_status()
            if margin_status:
                # Get optimized position size based on current margin level
                margin_position_calc = self.margin_manager.calculate_optimal_position_size(margin_status, direction)
                if margin_position_calc:
                    position_calc = margin_position_calc

                # Check if rebalancing is needed
                if self.margin_manager.check_rebalancing_needed(margin_status):
                    print("🔄 Automatic rebalancing triggered")
                    self.margin_manager.execute_rebalancing(margin_status)

                # Update margin tracking
                self.margin_manager.update_margin_tracking(margin_status)

        # Use intelligent portfolio rebalancer if available (cross margin mode)
        if self.portfolio_rebalancer and self.is_live_mode:
            # Get current portfolio status
            portfolio_status = self.portfolio_rebalancer.get_portfolio_status()
            if portfolio_status:
                # Check if portfolio rebalancing is needed
                needs_rebalance, reason = self.portfolio_rebalancer.check_rebalancing_needed(portfolio_status)

                if needs_rebalance:
                    print(f"🔄 Portfolio rebalancing needed: {reason}")

                    # Get rebalance calculation
                    rebalance_calc = self.portfolio_rebalancer.calculate_rebalance_amounts(portfolio_status)
                    if rebalance_calc:
                        # Execute portfolio rebalancing
                        success = self.portfolio_rebalancer.execute_rebalance(portfolio_status, rebalance_calc)
                        if success:
                            print("✅ Portfolio rebalanced successfully")
                        else:
                            print("❌ Portfolio rebalancing failed")

                # Check trading capacity
                trading_capacity = self.portfolio_rebalancer.check_trading_capacity(portfolio_status)
                if not trading_capacity['can_buy'] and direction == 'BUY':
                    print("⚠️ Insufficient USDT for BUY trade - portfolio rebalancing recommended")
                elif not trading_capacity['can_sell'] and direction == 'SELL':
                    print("⚠️ Insufficient BTC for SELL trade - portfolio rebalancing recommended")

                # Get optimized position size from portfolio rebalancer
                portfolio_position_calc = self.portfolio_rebalancer.calculate_optimal_position_size(portfolio_status, direction)
                if portfolio_position_calc:
                    # Convert to expected format for compatibility
                    position_calc = {
                        'position_size_btc': portfolio_position_calc['position_size_btc'],
                        'stop_distance_usd': portfolio_position_calc['risk_usd'] / portfolio_position_calc['position_size_btc'] if portfolio_position_calc['position_size_btc'] > 0 else 0,
                        'target_distance_usd': self.model.profit_target / portfolio_position_calc['position_size_btc'] if portfolio_position_calc['position_size_btc'] > 0 else 0,
                        'actual_risk': portfolio_position_calc['risk_usd'],
                        'expected_profit': self.model.profit_target
                    }

        # Get grid levels for this trade
        grid_levels = self.margin_calculator.calculate_grid_levels(entry_price, direction)

        # Calculate stop loss and take profit based on grid calculations
        if direction == 'BUY':
            stop_loss = entry_price - position_calc['stop_distance_usd']
            take_profit = entry_price + position_calc['target_distance_usd']
        else:
            stop_loss = entry_price + position_calc['stop_distance_usd']
            take_profit = entry_price - position_calc['target_distance_usd']

        # Use calculated position size
        quantity = position_calc['position_size_btc']
        risk_amount = position_calc['actual_risk']
        target_profit = position_calc['expected_profit']
        
        trade = LiveTrade(
            trade_id=trade_id,
            timestamp=datetime.now(),
            direction=direction,
            entry_price=entry_price,
            quantity=quantity,
            risk_amount=risk_amount,
            target_profit=target_profit,
            stop_loss=stop_loss,
            status='OPEN',
            confidence=confidence,
            binance_symbol="BTCUSDT"
        )
        
        # Place order on Binance in live mode
        if self.is_live_mode and binance_connector.is_connected:
            try:
                side = 'buy' if direction == 'BUY' else 'sell'
                binance_order = binance_connector.place_market_order(
                    trade.binance_symbol, side, quantity
                )
                if binance_order:
                    trade.binance_order_id = binance_order['id']
                    print(f"✅ LIVE ORDER PLACED: {binance_order['id']}")
                else:
                    print("❌ Failed to place live order - continuing in simulation")
            except Exception as e:
                print(f"❌ Live order error: {e} - continuing in simulation")
        
        self.open_trades.append(trade)
        self.last_trade_time = datetime.now()
        self.daily_trades += 1

        # Log trade to CSV for persistence
        if CSV_LOGGER_AVAILABLE:
            try:
                trade_data = {
                    'trade_id': trade.trade_id,
                    'direction': trade.direction,
                    'status': 'OPEN',
                    'entry_time': trade.timestamp.isoformat(),
                    'entry_date': trade.timestamp.strftime('%Y-%m-%d'),
                    'entry_price': trade.entry_price,
                    'quantity': trade.quantity,
                    'position_usd': trade.entry_price * trade.quantity,
                    'risk_amount': trade.risk_amount,
                    'target_profit': trade.target_profit,
                    'confidence': trade.confidence * 100,
                    'signal_confidence': trade.confidence * 100,
                    'leverage': getattr(trade, 'leverage', 1.0),
                    'stop_loss': trade.stop_loss,
                    'take_profit': getattr(trade, 'take_profit', 0),
                    'binance_order_id': getattr(trade, 'binance_order_id', 'Unknown'),
                    'is_margin_trade': True,  # Cross margin mode
                    'account_type': 'Cross Margin',
                    'margin_used': getattr(trade, 'margin_used', 0),
                    'model_prediction': f"{direction} Signal",
                    'is_open': True,
                    'is_live_trade': self.is_live_mode,
                    'current_price': entry_price,
                    'unrealized_pnl': 0.0,
                    'realized_pnl': 0.0,
                    'profit_percentage': 0.0
                }
                csv_logger.log_trade(trade_data)
                print(f"📝 Trade logged to CSV: {trade.trade_id}")
            except Exception as e:
                print(f"❌ Error logging trade to CSV: {e}")

        # Log to Database (primary storage)
        if DATABASE_AVAILABLE and trading_db:
            try:
                trading_db.insert_trade(trade_data)
                print(f"💾 Trade logged to Database: {trade.trade_id}")
            except Exception as e:
                print(f"❌ Error logging trade to Database: {e}")

        # Cache trade data for persistence during outages
        if CACHE_MANAGER_AVAILABLE:
            try:
                cache_trade_data = {
                    'trade_id': trade.trade_id,
                    'direction': trade.direction,
                    'entry_price': trade.entry_price,
                    'quantity': trade.quantity,
                    'timestamp': trade.timestamp.isoformat(),
                    'status': 'open',
                    'stop_loss': trade.stop_loss,
                    'target_profit': trade.target_profit,
                    'confidence': trade.confidence,
                    'is_live_trade': self.is_live_mode
                }
                cache_manager.cache_trade_data(trade.trade_id, cache_trade_data)
                print(f"💾 Trade cached for persistence: {trade.trade_id}")
            except Exception as e:
                print(f"❌ Error caching trade data: {e}")

        print(f"🔥 NEW TRADE: {trade_id} | {direction} | ${entry_price:,.2f} | Confidence: {confidence:.1%}")

        return trade
    
    def check_trade_exits(self):
        """Check if any open trades should be closed - Binance ready."""
        trades_to_close = []

        # Update real-time data for all open trades in CSV and Database
        for trade in self.open_trades:
            try:
                # Calculate current unrealized P&L
                if trade.direction == 'BUY':
                    unrealized_pnl = (self.current_price - trade.entry_price) * trade.quantity
                else:
                    unrealized_pnl = (trade.entry_price - self.current_price) * trade.quantity

                # Update CSV with real-time data (backward compatibility)
                if CSV_LOGGER_AVAILABLE and csv_logger:
                    csv_logger.update_open_trade_realtime(
                        trade.trade_id,
                        self.current_price,
                        unrealized_pnl
                    )

                # Update Database with real-time data (primary storage)
                if DATABASE_AVAILABLE and trading_db:
                    trading_db.update_open_trade_realtime(
                        trade.trade_id,
                        self.current_price,
                        unrealized_pnl
                    )

            except Exception as e:
                print(f"❌ Error updating real-time trade data: {e}")

        for trade in self.open_trades:
            should_close = False
            exit_price = self.current_price
            
            # Check profit target and stop loss using proper price levels
            if trade.direction == 'BUY':
                # Calculate take profit price: entry + (target_profit / quantity)
                take_profit_price = trade.entry_price + (trade.target_profit / trade.quantity)
                if self.current_price >= take_profit_price:
                    should_close = True
                    trade.status = 'CLOSED_PROFIT'
                elif self.current_price <= trade.stop_loss:
                    should_close = True
                    trade.status = 'CLOSED_LOSS'
            else:  # SELL
                # Calculate take profit price: entry - (target_profit / quantity)
                take_profit_price = trade.entry_price - (trade.target_profit / trade.quantity)
                if self.current_price <= take_profit_price:
                    should_close = True
                    trade.status = 'CLOSED_PROFIT'
                elif self.current_price >= trade.stop_loss:
                    should_close = True
                    trade.status = 'CLOSED_LOSS'
            
            # Time-based exit (max 12 hours for conservative)
            if (datetime.now() - trade.timestamp).total_seconds() > 12 * 3600:
                should_close = True
                if trade.status == 'OPEN':
                    # Determine profit/loss at current price using actual position size
                    if trade.direction == 'BUY':
                        pnl = (self.current_price - trade.entry_price) * trade.quantity
                    else:
                        pnl = (trade.entry_price - self.current_price) * trade.quantity
                    
                    trade.status = 'CLOSED_PROFIT' if pnl > 0 else 'CLOSED_LOSS'
            
            if should_close:
                # Close order on Binance in live mode
                if self.is_live_mode and binance_connector.is_connected and trade.binance_order_id:
                    try:
                        # Place opposite order to close position
                        close_side = 'sell' if trade.direction == 'BUY' else 'buy'
                        close_order = binance_connector.place_market_order(
                            trade.binance_symbol, close_side, trade.quantity
                        )
                        if close_order:
                            print(f"✅ LIVE POSITION CLOSED: {close_order['id']}")
                        else:
                            print("❌ Failed to close live position")
                    except Exception as e:
                        print(f"❌ Live close error: {e}")
                
                trade.exit_price = exit_price
                trade.exit_timestamp = datetime.now()
                
                # Calculate P&L using actual position size
                if trade.direction == 'BUY':
                    trade.pnl = (exit_price - trade.entry_price) * trade.quantity
                else:
                    trade.pnl = (trade.entry_price - exit_price) * trade.quantity
                
                # Update statistics
                self.total_profit += trade.pnl
                self.total_trades += 1
                
                if trade.pnl > 0:
                    self.winning_trades += 1
                    print(f"✅ PROFIT: {trade.trade_id} | ${trade.pnl:+.2f}")
                else:
                    self.losing_trades += 1
                    print(f"❌ LOSS: {trade.trade_id} | ${trade.pnl:+.2f}")
                
                trades_to_close.append(trade)
        
        # Move closed trades and update CSV (no duplicates)
        for trade in trades_to_close:
            self.open_trades.remove(trade)
            self.closed_trades.append(trade)

            # Update existing trade entry in CSV to CLOSED status (backward compatibility)
            if CSV_LOGGER_AVAILABLE and csv_logger:
                try:
                    csv_logger.close_trade(
                        trade.trade_id,
                        trade.exit_price,
                        trade.exit_timestamp.isoformat(),
                        trade.pnl
                    )
                    print(f"📝 Trade updated to CLOSED in CSV: {trade.trade_id}")
                except Exception as e:
                    print(f"❌ Error updating trade closure in CSV: {e}")

            # Update existing trade entry in Database to CLOSED status (primary storage)
            if DATABASE_AVAILABLE and trading_db:
                try:
                    trading_db.close_trade(
                        trade.trade_id,
                        trade.exit_price,
                        trade.exit_timestamp.isoformat(),
                        trade.pnl
                    )
                    print(f"💾 Trade updated to CLOSED in Database: {trade.trade_id}")
                except Exception as e:
                    print(f"❌ Error updating trade closure in Database: {e}")
        
        # Update equity and drawdown
        self.equity = self.balance + self.total_profit
        self.max_equity = max(self.max_equity, self.equity)
        current_drawdown = (self.max_equity - self.equity) / self.max_equity if self.max_equity > 0 else 0
        self.max_drawdown_value = max(self.max_drawdown_value, current_drawdown)
    
    def get_performance_stats(self):
        """Get current performance statistics with robust scoring."""
        win_rate = self.winning_trades / max(self.total_trades, 1)
        profit_factor = abs(self.total_profit / max(sum(t.pnl for t in self.closed_trades if t.pnl < 0), 1))

        # Daily statistics
        today = datetime.now().date()
        daily_trades = len([t for t in self.closed_trades if t.exit_timestamp and t.exit_timestamp.date() == today])
        daily_pnl = sum(t.pnl for t in self.closed_trades if t.exit_timestamp and t.exit_timestamp.date() == today)

        # Calculate robust score if available and sufficient trades
        robust_score = 0.0
        robust_components = {}

        if ROBUST_METRICS_AVAILABLE and len(self.closed_trades) >= 5:
            try:
                # Convert trades to format for robust calculator
                trade_data = []
                for trade in self.closed_trades:
                    trade_data.append({
                        'trade_id': trade.trade_id,
                        'realized_pnl': trade.pnl,
                        'entry_price': trade.entry_price,
                        'exit_price': trade.exit_price or trade.entry_price,
                        'direction': trade.direction,
                        'timestamp': trade.timestamp.isoformat(),
                        'exit_timestamp': trade.exit_timestamp.isoformat() if trade.exit_timestamp else None
                    })

                # Calculate robust score
                robust_results = robust_calculator.calculate_robust_score(trade_data)
                robust_score = robust_results.get('robust_score', 0.0)
                robust_components = robust_results.get('components', {})

            except Exception as e:
                print(f"⚠️ Error calculating robust score: {e}")
                robust_score = 0.0

        # Basic performance stats
        basic_stats = {
            'equity': round(self.equity, 2),
            'total_profit': round(self.total_profit, 2),
            'total_trades': self.total_trades,
            'win_rate': round(win_rate * 100, 1),
            'profit_factor': round(profit_factor, 2),
            'max_drawdown': round(self.max_drawdown_value * 100, 2),
            'daily_trades': daily_trades,
            'daily_pnl': round(daily_pnl, 2),
            'open_positions': len(self.open_trades),
            'available_balance': round(self.available_balance, 2),
            'account_size': self.model.account_size
        }

        # Add robust scoring metrics
        if robust_score > 0:
            basic_stats.update({
                'robust_score': round(robust_score * 100, 1),  # Convert to percentage
                'robust_components': {
                    'sortino_ratio': round(robust_components.get('sortino_ratio', 0), 2),
                    'ulcer_index': round(robust_components.get('ulcer_index', 0), 2),
                    'equity_curve_r2': round(robust_components.get('equity_curve_r2', 0) * 100, 1),
                    'profit_stability': round(robust_components.get('profit_stability', 0) * 100, 1),
                    'upward_move_ratio': round(robust_components.get('upward_move_ratio', 0) * 100, 1),
                    'drawdown_duration': round(robust_components.get('drawdown_duration', 0), 1)
                },
                'scoring_method': 'robust_metrics_system'
            })
        else:
            # Fallback to basic composite score
            basic_composite = (
                0.3 * min(win_rate / 0.6, 1.0) +  # Win rate component
                0.3 * min(max((self.equity - self.model.account_size) / self.model.account_size, 0) / 0.2, 1.0) +  # Growth
                0.2 * min(max(1 - self.max_drawdown_value, 0), 1.0) +  # Drawdown
                0.2 * min(self.total_trades / 50, 1.0)  # Activity
            )
            basic_stats.update({
                'robust_score': round(basic_composite * 100, 1),
                'scoring_method': 'basic_composite'
            })

        return basic_stats

class BinanceLiveConnector:
    """Live Binance API connector for real trading."""

    def __init__(self):
        self.exchange = None
        self.is_connected = False
        self.api_key = ""
        self.secret_key = ""
        self.testnet = True  # Default to testnet for safety

    def load_api_keys(self):
        """Load API keys from the BinanceAPI_2.txt file."""
        try:
            api_file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "BinanceAPI_2.txt")

            if os.path.exists(api_file_path):
                with open(api_file_path, 'r') as f:
                    lines = f.read().strip().split('\n')
                    if len(lines) >= 2:
                        self.api_key = lines[0].strip()
                        self.secret_key = lines[1].strip()
                        print("✅ API keys loaded from BinanceAPI_2.txt")
                        return True

            print("❌ Could not load API keys from BinanceAPI_2.txt")
            return False

        except Exception as e:
            print(f"❌ Error loading API keys: {e}")
            return False

    def _connect_simple_binance(self, testnet=True, use_margin=False):
        """Connect using Simple Binance Connector."""
        try:
            print("🔗 Connecting with Simple Binance Connector...")

            # Create simple connector
            api_key_file = r"C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt"
            self.simple_connector = create_simple_binance_connector(api_key_file, testnet)

            if self.simple_connector:
                self.is_connected = True
                self.testnet = testnet
                self.use_margin = use_margin
                print(f"✅ Simple Binance Connector established")
                print(f"   Testnet: {testnet}")
                print(f"   Cross Margin: {use_margin}")
                return True
            else:
                print("❌ Simple Binance Connector failed")
                return False

        except Exception as e:
            print(f"❌ Simple Binance connection error: {e}")
            return False

    def connect(self, testnet=True, use_margin=False):
        """Connect to Binance API with optional cross margin support."""
        if not CCXT_AVAILABLE and not SIMPLE_BINANCE_AVAILABLE:
            print("❌ No Binance connector available")
            return False

        if not self.load_api_keys():
            return False

        # Try Simple Binance Connector if CCXT not available
        if not CCXT_AVAILABLE and SIMPLE_BINANCE_AVAILABLE:
            return self._connect_simple_binance(testnet, use_margin)

        try:
            self.testnet = testnet
            self.use_margin = use_margin

            # Configure exchange options
            options = {
                'adjustForTimeDifference': True,
            }

            if use_margin:
                options['defaultType'] = 'margin'  # Use cross margin
                print("🔄 Configuring for CROSS MARGIN trading...")
            else:
                options['defaultType'] = 'spot'   # Use spot trading
                print("📊 Configuring for SPOT trading...")

            self.exchange = ccxt.binance({
                'apiKey': self.api_key,
                'secret': self.secret_key,
                'sandbox': testnet,  # Use testnet for safety
                'enableRateLimit': True,
                'options': options
            })

            # Test connection and get account info
            if use_margin:
                # Test margin account access
                balance = self.exchange.fetch_balance({'type': 'margin'})
                margin_info = balance.get('info', {})
                margin_level = float(margin_info.get('marginLevel', 0))

                print(f"✅ Connected to Binance {'TESTNET' if testnet else 'LIVE'} CROSS MARGIN")
                print(f"   Margin Level: {margin_level:.2f}")

                # Risk assessment
                if margin_level >= 3.0:
                    print("   Risk Level: ✅ LOW (Safe)")
                elif margin_level >= 1.5:
                    print("   Risk Level: ⚠️ MEDIUM")
                elif margin_level >= 1.1:
                    print("   Risk Level: 🚨 HIGH")
                else:
                    print("   Risk Level: 🔴 CRITICAL")

            else:
                # Test spot account access
                balance = self.exchange.fetch_balance()
                account_type = balance.get('info', {}).get('accountType', 'Unknown')
                print(f"✅ Connected to Binance {'TESTNET' if testnet else 'LIVE'} SPOT")
                print(f"   Account Type: {account_type}")

            self.is_connected = True
            return True

        except Exception as e:
            print(f"❌ Binance connection failed: {e}")
            self.is_connected = False
            return False

    def place_market_order(self, symbol, side, amount):
        """Place a market order on Binance."""
        if not self.is_connected:
            print("❌ Not connected to Binance")
            return None

        try:
            print(f"📤 Placing {side} order: {amount} {symbol}")

            order = self.exchange.create_market_order(symbol, side, amount)

            print(f"✅ Order placed successfully: {order['id']}")
            return order

        except Exception as e:
            print(f"❌ Order placement failed: {e}")
            return None

    def get_account_balance(self):
        """Get account balance."""
        if not self.is_connected:
            return None

        try:
            balance = self.exchange.fetch_balance()
            return balance
        except Exception as e:
            print(f"❌ Failed to get balance: {e}")
            return None

# Global trading engine, thread, and Binance connector
trading_engine = LiveTradingEngine(BestCompositeModel())
trading_thread = None
binance_connector = BinanceLiveConnector()

@app.route('/')
def index():
    """Main live trading dashboard with aggressive cache busting."""
    from flask import make_response
    import time
    import random

    # Create response with cache-busting headers
    response = make_response(render_template('bitcoin_freedom_dashboard.html',
                                           cache_buster=int(time.time() * 1000) + random.randint(1, 9999)))

    # Ultra-aggressive no caching
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0, private'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = time.strftime('%a, %d %b %Y %H:%M:%S GMT')
    response.headers['ETag'] = f'"{int(time.time() * 1000)}-{random.randint(1000, 9999)}"'
    response.headers['Vary'] = '*'

    return response

@app.route('/test')
def test():
    """Test endpoint to verify new template is working."""
    return render_template('bitcoin_freedom_dashboard.html')

@app.route('/new')
def new_dashboard():
    """New dashboard with fresh template."""
    from flask import make_response
    import time
    import random

    # Create response with ultra-aggressive cache-busting
    response = make_response(render_template('bitcoin_freedom_dashboard.html',
                                           cache_buster=int(time.time() * 1000) + random.randint(1, 9999)))

    # Ultra-aggressive no caching
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0, private'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = time.strftime('%a, %d %b %Y %H:%M:%S GMT')
    response.headers['ETag'] = f'"{int(time.time() * 1000)}-{random.randint(1000, 9999)}"'
    response.headers['Vary'] = '*'

    return response

@app.route('/api/models')
def get_available_models():
    """Get all available high-performance models."""
    try:
        models = {}
        for key, config in trading_engine.model.available_models.items():
            models[key] = {
                'name': config['name'],
                'composite_score': config['composite_score'],
                'win_rate': config['win_rate'],
                'net_profit': config['net_profit'],
                'trades_per_day': config['trades_per_day'],
                'sharpe_ratio': config['sharpe_ratio'],
                'risk_level': config['risk_level'],
                'live_ready': config['live_ready'],
                'active': key == trading_engine.model.active_model_key
            }

        return jsonify({
            'status': 'success',
            'models': models,
            'active_model': trading_engine.model.active_model_key
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/switch_model', methods=['POST'])
def switch_model():
    """Switch to a different high-performance model."""
    try:
        data = request.get_json()
        model_key = data.get('model_key')

        if not model_key:
            return jsonify({
                'status': 'error',
                'error': 'model_key is required'
            }), 400

        # Stop trading engine during model switch
        was_running = trading_engine.is_running
        if was_running:
            trading_engine.stop_trading()

        # Switch model
        success = trading_engine.model.switch_model(model_key)

        if success:
            # Restart trading engine if it was running
            if was_running:
                trading_engine.start_trading()

            return jsonify({
                'status': 'success',
                'message': f'Switched to model: {trading_engine.model.get_active_model()["name"]}',
                'active_model': trading_engine.model.active_model_key
            })
        else:
            return jsonify({
                'status': 'error',
                'error': f'Failed to switch to model: {model_key}'
            }), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/system_health')
def comprehensive_health_check():
    """Comprehensive system health check for real money trading readiness."""
    try:
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'components': {},
            'trading_readiness': True,
            'issues': []
        }

        # 1. Model Health Check
        try:
            model_health = {
                'status': 'healthy',
                'active_model': trading_engine.model.active_model_key,
                'model_name': trading_engine.model.model_type,
                'composite_score': trading_engine.model.composite_score,
                'win_rate': trading_engine.model.win_rate,
                'ai_ensemble_loaded': trading_engine.model.ai_ensemble is not None,
                'live_ready': trading_engine.model.get_active_model()['live_ready']
            }
            health_status['components']['model'] = model_health
        except Exception as e:
            health_status['components']['model'] = {'status': 'error', 'error': str(e)}
            health_status['issues'].append(f"Model health check failed: {e}")

        # 2. Data Cache Health Check
        try:
            if CACHE_MANAGER_AVAILABLE:
                cache_health = {
                    'status': 'healthy',
                    'available': True,
                    'latest_btc_price': cache_manager.get_latest_price('BTCUSDT'),
                    'cache_entries': 'active'
                }
            else:
                cache_health = {'status': 'unavailable', 'available': False}
                health_status['issues'].append("Data cache manager not available")
            health_status['components']['cache'] = cache_health
        except Exception as e:
            health_status['components']['cache'] = {'status': 'error', 'error': str(e)}

        # 3. Trading Engine Health Check
        try:
            engine_health = {
                'status': 'healthy',
                'running': trading_engine.is_running,
                'live_mode': trading_engine.is_live_mode,
                'balance': trading_engine.balance,
                'open_trades': len(trading_engine.open_trades),
                'total_trades': trading_engine.total_trades,
                'last_signal_time': getattr(trading_engine, 'last_signal_time', None)
            }
            health_status['components']['trading_engine'] = engine_health
        except Exception as e:
            health_status['components']['trading_engine'] = {'status': 'error', 'error': str(e)}
            health_status['issues'].append(f"Trading engine health check failed: {e}")

        # 4. Price Data Health Check
        try:
            current_price = trading_engine.get_current_btc_price()
            price_health = {
                'status': 'healthy' if current_price else 'warning',
                'current_btc_price': current_price,
                'price_available': current_price is not None,
                'last_update': getattr(trading_engine.price_fetcher, 'last_update', None)
            }
            if not current_price:
                health_status['issues'].append("BTC price data not available")
            health_status['components']['price_data'] = price_health
        except Exception as e:
            health_status['components']['price_data'] = {'status': 'error', 'error': str(e)}
            health_status['issues'].append(f"Price data health check failed: {e}")

        # 5. Database Health Check
        try:
            if DATABASE_AVAILABLE:
                db_health = {
                    'status': 'healthy',
                    'available': True,
                    'connection': 'active'
                }
            else:
                db_health = {'status': 'unavailable', 'available': False}
                health_status['issues'].append("Database not available")
            health_status['components']['database'] = db_health
        except Exception as e:
            health_status['components']['database'] = {'status': 'error', 'error': str(e)}

        # 6. Binance Connectivity Health Check
        try:
            binance_health = {
                'status': 'healthy',
                'connector_available': binance_connector is not None,
                'connected': getattr(binance_connector, 'is_connected', False),
                'live_trading_ready': False
            }

            if binance_connector and hasattr(binance_connector, 'is_connected'):
                binance_health['connected'] = binance_connector.is_connected
                binance_health['live_trading_ready'] = binance_connector.is_connected

            health_status['components']['binance'] = binance_health
        except Exception as e:
            health_status['components']['binance'] = {'status': 'error', 'error': str(e)}

        # Determine overall health
        component_statuses = [comp.get('status', 'unknown') for comp in health_status['components'].values()]
        if 'error' in component_statuses:
            health_status['overall_status'] = 'error'
            health_status['trading_readiness'] = False
        elif 'warning' in component_statuses or health_status['issues']:
            health_status['overall_status'] = 'warning'

        # Real money trading readiness assessment
        critical_components = ['model', 'trading_engine', 'price_data']
        for comp in critical_components:
            if health_status['components'].get(comp, {}).get('status') != 'healthy':
                health_status['trading_readiness'] = False
                break

        return jsonify(health_status)

    except Exception as e:
        return jsonify({
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'error',
            'error': str(e),
            'trading_readiness': False
        }), 500

@app.route('/health')
def health_check():
    """Enhanced health check endpoint with comprehensive system status."""
    global trading_engine, binance_connector, trading_db, cache_manager

    try:
        # Perform comprehensive health checks
        health_status = 'healthy'
        issues = []

        # Check trading engine
        engine_status = 'running' if trading_engine.is_running else 'stopped'

        # Check Binance connection
        binance_status = 'connected' if binance_connector.is_connected else 'disconnected'
        if binance_status == 'disconnected':
            issues.append('Binance API disconnected')

        # Check database
        database_status = 'available'
        if DATABASE_AVAILABLE and trading_db:
            try:
                # Test database connection
                trading_db.get_recent_trades(limit=1)
            except Exception as e:
                database_status = 'error'
                issues.append(f'Database error')
        elif not DATABASE_AVAILABLE:
            database_status = 'unavailable'
            issues.append('Database not available')

        # Check CSV logger
        csv_status = 'available' if CSV_LOGGER_AVAILABLE else 'unavailable'
        if not CSV_LOGGER_AVAILABLE:
            issues.append('CSV logger not available')

        # Check cache manager
        cache_status = 'available'
        if CACHE_MANAGER_AVAILABLE:
            try:
                # Test cache connection
                cache_manager.get_cached_price('BTC')
            except Exception as e:
                cache_status = 'error'
                issues.append(f'Cache error')
        else:
            cache_status = 'unavailable'

        # Check model status
        model_status = 'loaded'
        try:
            model_score = trading_engine.model.composite_score
            model_id = trading_engine.model.model_id
        except Exception as e:
            model_status = 'error'
            issues.append(f'Model error')

        # Check price data freshness
        price_status = 'current'
        try:
            current_price = trading_engine.current_price
            if current_price <= 0:
                price_status = 'stale'
                issues.append('Price data is stale')
        except Exception as e:
            price_status = 'error'
            issues.append(f'Price data error')

        # Determine overall health
        if len(issues) > 0:
            if any('error' in issue.lower() for issue in issues):
                health_status = 'critical'
            else:
                health_status = 'warning'

        return jsonify({
            'status': health_status,
            'timestamp': datetime.now().isoformat(),
            'webapp': 'running',
            'trading_engine': engine_status,
            'live_mode': trading_engine.is_live_mode if trading_engine else False,
            'binance_connector': binance_status,
            'database': database_status,
            'csv_logger': csv_status,
            'cache_manager': cache_status,
            'model_status': model_status,
            'price_status': price_status,
            'current_price': round(trading_engine.current_price, 2) if trading_engine.current_price > 0 else 0,
            'open_trades': len(trading_engine.open_trades) if trading_engine else 0,
            'issues': issues,
            'components': {
                'tcn_cnn_ppo_available': TCN_CNN_PPO_AVAILABLE,
                'robust_metrics_available': ROBUST_METRICS_AVAILABLE,
                'cache_manager_available': CACHE_MANAGER_AVAILABLE,
                'database_available': DATABASE_AVAILABLE,
                'csv_logger_available': CSV_LOGGER_AVAILABLE
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'critical',
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'issues': ['System exception occurred']
        }), 500

@app.route('/ping')
def ping():
    """Simple ping endpoint."""
    return jsonify({'status': 'pong', 'timestamp': datetime.now().isoformat()})

@app.route('/api/start_trading', methods=['POST'])
def start_trading():
    """Start the live trading engine."""
    global trading_engine, trading_thread

    if not trading_engine.is_running:
        trading_engine.is_running = True

        # Create and start background trading thread with proper error handling
        print(f"🔧 DEBUG: Starting trading thread at {datetime.now().strftime('%H:%M:%S')}")

        try:
            trading_thread = threading.Thread(target=live_trading_loop, daemon=True, name="LiveTradingLoop")
            trading_thread.start()

            # Give thread a moment to start
            time.sleep(0.5)

            if trading_thread.is_alive():
                print(f"✅ DEBUG: Trading thread started successfully")
                return jsonify({'status': 'success', 'message': 'Live trading started'})
            else:
                print(f"❌ DEBUG: Trading thread failed to start")
                trading_engine.is_running = False
                return jsonify({'status': 'error', 'message': 'Failed to start trading thread'})

        except Exception as e:
            print(f"❌ DEBUG: Error starting trading thread: {e}")
            trading_engine.is_running = False
            return jsonify({'status': 'error', 'message': f'Thread creation error: {str(e)}'})
    else:
        return jsonify({'status': 'error', 'message': 'Trading already running'})

@app.route('/api/stop_trading', methods=['POST'])
def stop_trading():
    """Stop the live trading engine."""
    global trading_engine
    trading_engine.is_running = False
    return jsonify({'status': 'success', 'message': 'Live trading stopped'})

@app.route('/api/toggle_live_mode', methods=['POST'])
def toggle_live_mode():
    """Toggle between live and simulation mode with cross margin support."""
    global trading_engine, binance_connector

    data = request.get_json()
    live_mode = data.get('live_mode', False)
    testnet = data.get('testnet', True)  # Default to testnet for safety
    use_margin = data.get('use_margin', False)  # Cross margin option

    if live_mode:
        # Connect to Binance when enabling live mode
        if binance_connector.connect(testnet=testnet, use_margin=use_margin):
            trading_engine.is_live_mode = True

            # Initialize intelligent margin manager for cross margin trading
            if use_margin and MARGIN_MANAGER_AVAILABLE:
                trading_engine.margin_manager = IntelligentMarginManager(
                    binance_connector,
                    trading_engine.model.risk_per_trade,
                    trading_engine.model.profit_target
                )
                print("🤖 Intelligent Margin Manager activated")

                # Check current margin level and provide guidance
                if binance_connector.is_connected:
                    try:
                        margin_status = trading_engine.margin_manager.get_margin_status()
                        if margin_status:
                            margin_level = margin_status['margin_level']
                            print(f"📊 Current Margin Level: {margin_level:.2f}")

                            if margin_level < 1.3:
                                print("🚨 WARNING: Margin level very low - emergency mode activated")
                                print("   System will use minimal position sizes for safety")
                            elif margin_level < 1.5:
                                print("⚠️ CAUTION: Margin level low - conservative trading activated")
                                print("   System will use reduced position sizes")
                            elif margin_level < 2.0:
                                print("📊 INFO: Margin level moderate - cautious trading mode")
                                print("   System will use moderate position sizes")
                            else:
                                print("✅ GOOD: Margin level acceptable for normal trading")
                    except Exception as e:
                        print(f"⚠️ Could not check margin level: {e}")

            elif use_margin and not MARGIN_MANAGER_AVAILABLE:
                print("⚠️ Cross margin mode enabled but advanced margin manager not available")

            # Initialize intelligent portfolio rebalancer - works with both CCXT and Simple Binance Connector
            if PORTFOLIO_REBALANCER_AVAILABLE and binance_connector and binance_connector.is_connected:
                try:
                    trading_engine.portfolio_rebalancer = IntelligentPortfolioRebalancer(
                        binance_connector,
                        target_btc_ratio=0.5,  # 50% BTC, 50% USDT
                        risk_per_trade=trading_engine.model.risk_per_trade
                    )
                    print("🔄 Intelligent Portfolio Rebalancer activated")
                    print("   Target: 50% BTC / 50% USDT for optimal trading")
                    print("   Compatible with Simple Binance Connector")
                    print("   Handles 'no USDT left' scenarios automatically")
                except Exception as e:
                    print(f"⚠️ Portfolio Rebalancer initialization failed: {e}")
                    trading_engine.portfolio_rebalancer = None
            elif not PORTFOLIO_REBALANCER_AVAILABLE:
                print("⚠️ Portfolio rebalancer not available")
            elif not binance_connector or not binance_connector.is_connected:
                print("⚠️ Portfolio rebalancer requires active Binance connection")

            # Determine mode description
            if testnet:
                mode = "TESTNET CROSS MARGIN" if use_margin else "TESTNET SPOT"
            else:
                mode = "LIVE CROSS MARGIN" if use_margin else "LIVE SPOT"

            return jsonify({
                'status': 'success',
                'message': f'Connected to Binance {mode}',
                'live_mode': True,
                'testnet': testnet,
                'use_margin': use_margin,
                'connected': True
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to connect to Binance',
                'live_mode': False,
                'connected': False
            })
    else:
        # Disable live mode
        trading_engine.is_live_mode = False
        return jsonify({
            'status': 'success',
            'message': 'Switched to SIMULATION mode',
            'live_mode': False,
            'connected': binance_connector.is_connected
        })

@app.route('/api/binance_status')
def binance_status():
    """Get Binance connection status and account info."""
    global binance_connector

    status = {
        'ccxt_available': CCXT_AVAILABLE,
        'simple_binance_available': SIMPLE_BINANCE_AVAILABLE,
        'connected': binance_connector.is_connected,
        'testnet': binance_connector.testnet if binance_connector.is_connected else None,
        'connector_type': 'CCXT' if CCXT_AVAILABLE and hasattr(binance_connector, 'exchange') else 'Simple Binance' if SIMPLE_BINANCE_AVAILABLE else 'None',
        'account_balance': None
    }

    if binance_connector.is_connected:
        try:
            balance = binance_connector.get_account_balance()
            if balance:
                status['account_balance'] = {
                    'USDT': balance.get('USDT', {}).get('free', 0),
                    'BTC': balance.get('BTC', {}).get('free', 0),
                    'total_usdt': balance.get('USDT', {}).get('total', 0)
                }
        except Exception as e:
            print(f"Error getting balance: {e}")

    return jsonify(status)

@app.route('/api/margin_status')
def margin_status():
    """Get intelligent margin manager status."""
    global trading_engine

    try:
        if not trading_engine.margin_manager:
            return jsonify({
                'margin_manager_active': False,
                'message': 'Margin manager not active (not in cross margin mode)'
            })

        # Get margin status
        margin_status = trading_engine.margin_manager.get_margin_status()
        if not margin_status:
            return jsonify({
                'margin_manager_active': True,
                'error': 'Could not retrieve margin status'
            })

        # Get trading recommendation
        recommendation = trading_engine.margin_manager.get_trading_recommendation(margin_status)

        # Generate status report
        status_report = trading_engine.margin_manager.get_status_report(margin_status)

        return jsonify({
            'margin_manager_active': True,
            'margin_level': margin_status['margin_level'],
            'total_net_usd': margin_status['total_net_usd'],
            'btc_price': margin_status['btc_price'],
            'trading_status': recommendation['trading_status'] if recommendation else 'UNKNOWN',
            'risk_assessment': recommendation['risk_assessment'] if recommendation else 'UNKNOWN',
            'rebalancing_needed': recommendation['rebalancing_needed'] if recommendation else False,
            'status_report': status_report,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'margin_manager_active': bool(trading_engine.margin_manager),
            'error': str(e)
        })

@app.route('/api/portfolio_status')
def portfolio_status():
    """Get intelligent portfolio rebalancer status."""
    global trading_engine

    try:
        if not trading_engine.portfolio_rebalancer:
            return jsonify({
                'portfolio_rebalancer_active': False,
                'message': 'Portfolio rebalancer not active (not in cross margin mode)'
            })

        # Get portfolio status
        portfolio_status = trading_engine.portfolio_rebalancer.get_portfolio_status()
        if not portfolio_status:
            return jsonify({
                'portfolio_rebalancer_active': True,
                'error': 'Could not retrieve portfolio status'
            })

        # Get rebalancing recommendation
        recommendation = trading_engine.portfolio_rebalancer.get_rebalance_recommendation(portfolio_status)

        # Get trading capacity
        trading_capacity = trading_engine.portfolio_rebalancer.check_trading_capacity(portfolio_status)

        # Generate status report
        status_report = trading_engine.portfolio_rebalancer.get_status_report(portfolio_status)

        return jsonify({
            'portfolio_rebalancer_active': True,
            'total_value_usd': portfolio_status['total_value_usd'],
            'btc_net': portfolio_status['btc_net'],
            'usdt_net': portfolio_status['usdt_net'],
            'btc_ratio': portfolio_status['btc_ratio'],
            'usdt_ratio': portfolio_status['usdt_ratio'],
            'btc_imbalance': portfolio_status['btc_imbalance'],
            'needs_rebalance': recommendation['needs_rebalance'] if recommendation else False,
            'rebalance_reason': recommendation['reason'] if recommendation else 'UNKNOWN',
            'can_buy': trading_capacity['can_buy'],
            'can_sell': trading_capacity['can_sell'],
            'max_buy_trades': trading_capacity['max_buy_trades'],
            'max_sell_trades': trading_capacity['max_sell_trades'],
            'status_report': status_report,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'portfolio_rebalancer_active': bool(trading_engine.portfolio_rebalancer),
            'error': str(e)
        })

@app.route('/api/trading_status')
def trading_status():
    """Get current trading status and performance."""
    global trading_engine

    stats = trading_engine.get_performance_stats()

    return jsonify({
        'is_running': trading_engine.is_running,
        'is_live_mode': trading_engine.is_live_mode,
        'binance_connected': binance_connector.is_connected,
        'binance_testnet': binance_connector.testnet if binance_connector.is_connected else None,
        'current_price': round(trading_engine.current_price, 2),
        'model_info': {
            'model_id': trading_engine.model.model_id,
            'model_type': trading_engine.model.model_type,
            'cycle': trading_engine.model.cycle,
            'composite_score': round(trading_engine.model.composite_score * 100, 1),
            'net_profit_target': getattr(trading_engine.model, 'net_profit', 4476.37),
            'win_rate_target': round(trading_engine.model.win_rate * 100, 1),
            'trades_per_day_target': trading_engine.model.trades_per_day,
            'tcn_weight': round(trading_engine.model.tcn_weight * 100, 1),
            'cnn_weight': round(trading_engine.model.cnn_weight * 100, 1),
            'ppo_weight': round(trading_engine.model.ppo_weight * 100, 1),
            'live_ready': trading_engine.model.live_trading_ready
        },
        'performance': stats,
        'risk_management': {
            'max_daily_trades': 'UNLIMITED',  # No daily trade limits
            'max_open_positions': trading_engine.max_open_positions,
            'daily_loss_limit': trading_engine.daily_loss_limit,
            'daily_profit_target': 'NONE'  # No daily profit targets
        }
    })

@app.route('/api/open_positions')
def open_positions():
    """Get current open positions."""
    global trading_engine

    positions = []
    for trade in trading_engine.open_trades:
        # Calculate current P&L using actual position size
        if trade.direction == 'BUY':
            current_pnl = (trading_engine.current_price - trade.entry_price) * trade.quantity
        else:
            current_pnl = (trade.entry_price - trading_engine.current_price) * trade.quantity

        positions.append({
            'trade_id': trade.trade_id,
            'direction': trade.direction,
            'entry_price': round(trade.entry_price, 2),
            'current_price': round(trading_engine.current_price, 2),
            'quantity': round(trade.quantity, 6),
            'current_pnl': round(current_pnl, 2),
            'target_profit': round(trade.target_profit, 2),
            'stop_loss': round(trade.stop_loss, 2),
            'confidence': round(trade.confidence * 100, 1),
            'duration': str(datetime.now() - trade.timestamp).split('.')[0],
            'status': trade.status,
            'binance_symbol': trade.binance_symbol
        })

    return jsonify(positions)

@app.route('/api/recent_trades')
def recent_trades():
    """Get recent trades with comprehensive details - single entry per trade (no duplicates)."""
    global trading_engine

    trades = []

    # Load trades from Database (primary storage) or CSV (fallback)
    if DATABASE_AVAILABLE and trading_db:
        try:
            db_trades = trading_db.get_recent_trades(limit=20)

            print(f"📊 Loaded {len(db_trades)} trades from Database")
            print(f"🔍 DEBUG: Converted {len(db_trades)} trades for API response")
            if db_trades:
                print(f"🔍 DEBUG: First trade data: {db_trades[0]}")

            for db_trade in db_trades:
                # Convert Database trade to API format
                trade_data = {
                    'trade_id': db_trade.get('trade_id', 'Unknown'),
                    'direction': db_trade.get('direction', 'Unknown'),
                    'entry_price': db_trade.get('entry_price', 0),
                    'exit_price': db_trade.get('exit_price', 0) if db_trade.get('exit_price') else None,
                    'current_price': db_trade.get('current_price', trading_engine.current_price),
                    'position_size': db_trade.get('quantity', 0),
                    'position_usd': db_trade.get('position_usd', 0),
                    'pnl': db_trade.get('pnl', 0),  # Use the pnl field directly from Database
                    'status': db_trade.get('status', 'Unknown'),
                    'confidence': db_trade.get('signal_confidence', 0),
                    'entry_time': db_trade.get('entry_time', '').split('T')[1][:8] if 'T' in str(db_trade.get('entry_time', '')) else db_trade.get('entry_time', 'Unknown'),
                    'exit_time': db_trade.get('exit_time', '').split('T')[1][:8] if db_trade.get('exit_time') and 'T' in str(db_trade.get('exit_time', '')) else None,
                    'entry_date': db_trade.get('entry_date', 'Unknown'),
                    'exit_date': db_trade.get('exit_date', None),
                    'duration': db_trade.get('duration', 'Unknown'),
                    'risk_amount': db_trade.get('risk_amount', 4.0),
                    'target_profit': db_trade.get('target_profit', 25.0),
                    'profit_percentage': db_trade.get('profit_percentage', 0),
                    'is_open': db_trade.get('is_open', False),
                    'unrealized_pnl': db_trade.get('unrealized_pnl', 0),
                    'realized_pnl': db_trade.get('realized_pnl', 0),
                    'leverage': db_trade.get('leverage', 1.0),
                    'stop_loss': db_trade.get('stop_loss', 0),
                    'take_profit': db_trade.get('take_profit', 0),
                    'binance_order_id': db_trade.get('binance_order_id', 'Unknown'),
                    'is_margin_trade': db_trade.get('is_margin_trade', True),
                    'account_type': db_trade.get('account_type', 'Cross Margin'),
                    'margin_used': db_trade.get('margin_used', 0),
                    'signal_confidence': db_trade.get('signal_confidence', 0),
                    'model_prediction': db_trade.get('model_prediction', 'Unknown')
                }

                trades.append(trade_data)

            print(f"📊 Loaded {len(db_trades)} unique trades from Database")
            print(f"🔍 DEBUG: Converted {len(trades)} trades for API response")
            if trades:
                print(f"🔍 DEBUG: First trade data: {trades[0]}")
        except Exception as e:
            print(f"❌ Error loading trades from Database: {e}")

    # Fallback to CSV if database not available or failed
    elif CSV_LOGGER_AVAILABLE and csv_logger:
        try:
            csv_trades = csv_logger.load_all_trades()

            # Sort by entry time (most recent first, open trades at top)
            csv_trades.sort(key=lambda x: (not x.get('is_open', False), x.get('entry_date', ''), x.get('entry_time', '')), reverse=True)

            # Take all trades (up to 20 most recent)
            recent_csv_trades = csv_trades[:20] if len(csv_trades) > 20 else csv_trades

            for csv_trade in recent_csv_trades:
                # Convert CSV trade to API format
                trade_data = {
                    'trade_id': csv_trade.get('trade_id', 'Unknown'),
                    'direction': csv_trade.get('direction', 'Unknown'),
                    'entry_price': csv_trade.get('entry_price', 0),
                    'exit_price': csv_trade.get('exit_price', 0) if csv_trade.get('exit_price') else None,
                    'current_price': csv_trade.get('current_price', trading_engine.current_price),
                    'position_size': csv_trade.get('quantity', 0),
                    'position_usd': csv_trade.get('position_usd', 0),
                    'pnl': csv_trade.get('pnl', 0),  # Use the pnl field directly from CSV
                    'status': csv_trade.get('status', 'Unknown'),
                    'confidence': csv_trade.get('signal_confidence', 0),
                    'entry_time': csv_trade.get('entry_time', '').split('T')[1][:8] if 'T' in str(csv_trade.get('entry_time', '')) else csv_trade.get('entry_time', 'Unknown'),
                    'exit_time': csv_trade.get('exit_time', '').split('T')[1][:8] if csv_trade.get('exit_time') and 'T' in str(csv_trade.get('exit_time', '')) else None,
                    'entry_date': csv_trade.get('entry_date', 'Unknown'),
                    'exit_date': csv_trade.get('exit_date', None),
                    'duration': csv_trade.get('duration', 'Unknown'),
                    'risk_amount': csv_trade.get('risk_amount', 4.0),
                    'target_profit': csv_trade.get('target_profit', 25.0),
                    'profit_percentage': csv_trade.get('profit_percentage', 0),
                    'is_open': csv_trade.get('is_open', False),
                    'unrealized_pnl': csv_trade.get('unrealized_pnl', 0),
                    'realized_pnl': csv_trade.get('realized_pnl', 0),
                    'leverage': csv_trade.get('leverage', 1.0),
                    'stop_loss': csv_trade.get('stop_loss', 0),
                    'take_profit': csv_trade.get('take_profit', 0),
                    'binance_order_id': csv_trade.get('binance_order_id', 'Unknown'),
                    'is_margin_trade': csv_trade.get('is_margin_trade', True),
                    'account_type': csv_trade.get('account_type', 'Cross Margin'),
                    'margin_used': csv_trade.get('margin_used', 0),
                    'signal_confidence': csv_trade.get('signal_confidence', 0),
                    'model_prediction': csv_trade.get('model_prediction', 'Unknown')
                }

                trades.append(trade_data)

            print(f"📊 Loaded {len(csv_trades)} unique trades from CSV (fallback)")
        except Exception as e:
            print(f"❌ Error loading trades from CSV: {e}")

    # Fallback: Add open trades from memory if CSV not available or empty
    if not trades:
        for trade in trading_engine.open_trades:
            # Calculate duration for open trade
            duration_str = "N/A"
            if trade.timestamp:
                duration_delta = datetime.now() - trade.timestamp
                total_seconds = int(duration_delta.total_seconds())
                if total_seconds < 60:
                    duration_str = f"{total_seconds}s"
                elif total_seconds < 3600:
                    minutes = total_seconds // 60
                    seconds = total_seconds % 60
                    duration_str = f"{minutes}m {seconds}s"
                else:
                    hours = total_seconds // 3600
                    minutes = (total_seconds % 3600) // 60
                    duration_str = f"{hours}h {minutes}m"

            # Calculate unrealized P&L
            unrealized_pnl = 0
            if trade.direction == 'BUY':
                unrealized_pnl = (trading_engine.current_price - trade.entry_price) * trade.quantity
            else:  # SELL
                unrealized_pnl = (trade.entry_price - trading_engine.current_price) * trade.quantity

            trades.append({
            'trade_id': trade.trade_id,
            'direction': trade.direction,
            'entry_price': round(trade.entry_price, 2),
            'exit_price': None,  # Still open
            'current_price': round(trading_engine.current_price, 2),
            'position_size': round(trade.quantity, 6),  # BTC quantity
            'position_usd': round(trade.entry_price * trade.quantity, 2),  # USD value
            'pnl': round(unrealized_pnl, 2),  # Unrealized P&L
            'status': 'OPEN',
            'confidence': round(trade.confidence * 100, 1),
            'entry_time': trade.timestamp.strftime('%H:%M:%S') if trade.timestamp else 'Unknown',
            'exit_time': None,  # Still open
            'entry_date': trade.timestamp.strftime('%Y-%m-%d') if trade.timestamp else 'Unknown',
            'exit_date': None,  # Still open
            'duration': duration_str,
            'risk_amount': getattr(trade, 'risk_amount', 4.0),  # Conservative risk for margin 1.44
            'target_profit': getattr(trade, 'target_profit', 25.0),
            'profit_percentage': round((unrealized_pnl / (trade.entry_price * trade.quantity)) * 100, 2) if unrealized_pnl else 0,

            # Enhanced details for open trades
            'is_open': True,
            'unrealized_pnl': round(unrealized_pnl, 2),
            'realized_pnl': 0,
            'leverage': getattr(trade, 'leverage', 1.0),
            'stop_loss': getattr(trade, 'stop_loss', 0),
            'take_profit': getattr(trade, 'take_profit', 0),
            'binance_order_id': getattr(trade, 'binance_order_id', 'Unknown'),
            'is_margin_trade': getattr(trade, 'is_margin_trade', True),  # Cross margin mode
            'account_type': 'Cross Margin',
            'margin_used': getattr(trade, 'margin_used', 0),
            'signal_confidence': round(trade.confidence * 100, 1),
            'model_prediction': getattr(trade, 'model_prediction', 'Unknown')
        })

    # Add closed trades
    for trade in trading_engine.closed_trades[-20:]:  # Last 20 trades
        # Calculate duration in a more readable format
        duration_str = "N/A"
        if trade.exit_timestamp:
            duration_delta = trade.exit_timestamp - trade.timestamp
            total_seconds = int(duration_delta.total_seconds())
            if total_seconds < 60:
                duration_str = f"{total_seconds}s"
            elif total_seconds < 3600:
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                duration_str = f"{minutes}m {seconds}s"
            else:
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                duration_str = f"{hours}h {minutes}m"

        trades.append({
            'trade_id': trade.trade_id,
            'direction': trade.direction,
            'entry_price': round(trade.entry_price, 2),
            'exit_price': round(trade.exit_price, 2) if trade.exit_price else None,
            'current_price': round(trading_engine.current_price, 2),
            'position_size': round(trade.quantity, 6),  # BTC quantity
            'position_usd': round(trade.entry_price * trade.quantity, 2),  # USD value
            'pnl': round(trade.pnl, 2),
            'status': trade.status,
            'confidence': round(trade.confidence * 100, 1),
            'entry_time': trade.timestamp.strftime('%H:%M:%S'),
            'exit_time': trade.exit_timestamp.strftime('%H:%M:%S') if trade.exit_timestamp else None,
            'entry_date': trade.timestamp.strftime('%Y-%m-%d'),
            'exit_date': trade.exit_timestamp.strftime('%Y-%m-%d') if trade.exit_timestamp else None,
            'duration': duration_str,
            'risk_amount': getattr(trade, 'risk_amount', 4.0),  # Conservative risk for margin 1.44
            'target_profit': getattr(trade, 'target_profit', 25.0),
            'profit_percentage': round((trade.pnl / (trade.entry_price * trade.quantity)) * 100, 2) if trade.pnl else 0,

            # Enhanced details for closed trades
            'is_open': False,
            'unrealized_pnl': 0,
            'realized_pnl': round(trade.pnl, 2),
            'leverage': getattr(trade, 'leverage', 1.0),
            'stop_loss': getattr(trade, 'stop_loss', 0),
            'take_profit': getattr(trade, 'take_profit', 0),
            'binance_order_id': getattr(trade, 'binance_order_id', 'Unknown'),
            'is_margin_trade': getattr(trade, 'is_margin_trade', True),  # Cross margin mode
            'account_type': 'Cross Margin',
            'margin_used': getattr(trade, 'margin_used', 0),
            'signal_confidence': round(trade.confidence * 100, 1),
            'model_prediction': getattr(trade, 'model_prediction', 'Unknown')
        })

    # Sort by entry time (most recent first, open trades at top)
    trades.sort(key=lambda x: (not x['is_open'], x.get('entry_date', ''), x.get('entry_time', '')), reverse=True)

    return jsonify(trades)

@app.route('/api/csv_backup', methods=['POST'])
def csv_backup():
    """Create a backup of the CSV trade log."""
    if not CSV_LOGGER_AVAILABLE:
        return jsonify({'status': 'error', 'message': 'CSV logger not available'})

    try:
        backup_path = csv_logger.backup_csv()
        if backup_path:
            return jsonify({
                'status': 'success',
                'message': f'CSV backup created: {backup_path}',
                'backup_path': backup_path
            })
        else:
            return jsonify({'status': 'error', 'message': 'Failed to create backup'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Backup error: {str(e)}'})

@app.route('/api/csv_statistics')
def csv_statistics():
    """Get trading statistics from CSV data."""
    if not CSV_LOGGER_AVAILABLE:
        return jsonify({'csv_available': False, 'message': 'CSV logger not available'})

    try:
        stats = csv_logger.get_trade_statistics()
        return jsonify({
            'csv_available': True,
            'statistics': stats,
            'csv_file': csv_logger.csv_file_path
        })
    except Exception as e:
        return jsonify({'csv_available': False, 'error': str(e)})

@app.route('/api/price_history')
def price_history():
    """Get price history for charting."""
    global trading_engine

    # Return last 100 price points
    history = trading_engine.price_history[-100:]

    return jsonify([
        {
            'timestamp': point['timestamp'].isoformat(),
            'price': round(point['price'], 2)
        }
        for point in history
    ])

@app.route('/api/force_test_trade', methods=['POST'])
def force_test_trade():
    """Force a test trade to verify the system is working."""
    global trading_engine

    if not trading_engine.is_running:
        return jsonify({'status': 'error', 'message': 'Trading engine not running'})

    try:
        data = request.get_json()
        direction = data.get('direction', 'BUY')  # Default to BUY

        # Force a high confidence signal
        forced_confidence = 0.85  # 85% confidence (well above 40% threshold)

        print(f"🧪 FORCING TEST TRADE: {direction} | Confidence: {forced_confidence:.1%}")

        # Force enter trade regardless of normal conditions
        trade = trading_engine.enter_trade(direction, forced_confidence)

        if trade:
            return jsonify({
                'status': 'success',
                'message': f'Test trade forced: {direction}',
                'trade_id': trade.trade_id,
                'direction': trade.direction,
                'entry_price': round(trade.entry_price, 2),
                'confidence': round(trade.confidence * 100, 1),
                'risk_amount': trade.risk_amount,
                'target_profit': trade.target_profit
            })
        else:
            return jsonify({'status': 'error', 'message': 'Failed to create test trade'})

    except Exception as e:
        print(f"❌ Force test trade error: {e}")
        return jsonify({'status': 'error', 'message': f'Error: {str(e)}'})

@app.route('/api/close_all_positions', methods=['POST'])
def close_all_positions():
    """Force close all open positions for testing."""
    global trading_engine

    if not trading_engine.is_running:
        return jsonify({'status': 'error', 'message': 'Trading engine not running'})

    try:
        closed_count = 0

        # Force close all open trades
        for trade in trading_engine.open_trades[:]:  # Copy list to avoid modification during iteration
            trade.exit_price = trading_engine.current_price
            trade.exit_timestamp = datetime.now()
            trade.status = 'CLOSED_TEST'

            # Calculate P&L using actual position size
            if trade.direction == 'BUY':
                trade.pnl = (trade.exit_price - trade.entry_price) * trade.quantity
            else:
                trade.pnl = (trade.entry_price - trade.exit_price) * trade.quantity

            # Update statistics
            trading_engine.total_profit += trade.pnl
            trading_engine.total_trades += 1

            if trade.pnl > 0:
                trading_engine.winning_trades += 1
                print(f"✅ TEST CLOSE PROFIT: {trade.trade_id} | ${trade.pnl:+.2f}")
            else:
                trading_engine.losing_trades += 1
                print(f"❌ TEST CLOSE LOSS: {trade.trade_id} | ${trade.pnl:+.2f}")

            # Move to closed trades
            trading_engine.open_trades.remove(trade)
            trading_engine.closed_trades.append(trade)
            closed_count += 1

        # Update equity
        trading_engine.equity = trading_engine.balance + trading_engine.total_profit

        return jsonify({
            'status': 'success',
            'message': f'Closed {closed_count} test positions',
            'closed_count': closed_count
        })

    except Exception as e:
        print(f"❌ Close all positions error: {e}")
        return jsonify({'status': 'error', 'message': f'Error: {str(e)}'})

@app.route('/api/test_trading_cycle', methods=['POST'])
def test_trading_cycle():
    """Run a complete test trading cycle: open position -> wait -> close."""
    global trading_engine

    if not trading_engine.is_running:
        return jsonify({'status': 'error', 'message': 'Trading engine not running'})

    try:
        # Force a BUY trade
        buy_trade = trading_engine.enter_trade('BUY', 0.85)

        if not buy_trade:
            return jsonify({'status': 'error', 'message': 'Failed to create test BUY trade'})

        # NO PRICE MANIPULATION - Test with real market conditions only
        print(f"🧪 TEST TRADE CREATED: {buy_trade.trade_id}")
        print(f"   Entry Price: ${buy_trade.entry_price:,.2f}")
        print(f"   Target Profit: ${buy_trade.target_profit}")
        print(f"   Stop Loss: ${buy_trade.stop_loss:,.2f}")
        print("   ⚠️ Trade will close naturally based on real market movement")
        print("   ⚠️ No artificial price manipulation - using real BTC price only")

        return jsonify({
            'status': 'success',
            'message': 'Test trade created - will close based on real market movement',
            'trade_id': buy_trade.trade_id,
            'entry_price': round(buy_trade.entry_price, 2),
            'target_profit': round(buy_trade.target_profit, 2),
            'stop_loss': round(buy_trade.stop_loss, 2),
            'note': 'No price manipulation - using real market data only'
        })

    except Exception as e:
        print(f"❌ Test trading cycle error: {e}")
        return jsonify({'status': 'error', 'message': f'Error: {str(e)}'})

@app.route('/api/reset_trading_stats', methods=['POST'])
def reset_trading_stats():
    """Reset all trading statistics for fresh testing."""
    global trading_engine, trading_db, csv_logger

    try:
        # Reset all statistics
        trading_engine.total_profit = 0.0
        trading_engine.total_trades = 0
        trading_engine.winning_trades = 0
        trading_engine.losing_trades = 0
        trading_engine.daily_trades = 0
        trading_engine.equity = trading_engine.model.account_size
        trading_engine.balance = trading_engine.model.account_size
        trading_engine.max_equity = trading_engine.model.account_size
        trading_engine.max_drawdown_value = 0.0

        # Clear trade history from memory
        trading_engine.open_trades.clear()
        trading_engine.closed_trades.clear()

        # Clear database records
        cleared_db = False
        if DATABASE_AVAILABLE and trading_db:
            try:
                trading_db.clear_all_trades()
                cleared_db = True
                print("🗄️ Database trades cleared")
            except Exception as e:
                print(f"⚠️ Database clear error: {e}")

        # Clear CSV records
        cleared_csv = False
        if CSV_LOGGER_AVAILABLE and csv_logger:
            try:
                csv_logger.clear_all_trades()
                cleared_csv = True
                print("📄 CSV trades cleared")
            except Exception as e:
                print(f"⚠️ CSV clear error: {e}")

        print("🔄 TRADING STATS RESET - Fresh start for testing")
        print(f"   ✅ Memory: Cleared")
        print(f"   {'✅' if cleared_db else '❌'} Database: {'Cleared' if cleared_db else 'Failed'}")
        print(f"   {'✅' if cleared_csv else '❌'} CSV: {'Cleared' if cleared_csv else 'Failed'}")

        return jsonify({
            'status': 'success',
            'message': 'Trading statistics and history reset successfully',
            'account_size': trading_engine.model.account_size,
            'cleared': {
                'memory': True,
                'database': cleared_db,
                'csv': cleared_csv
            }
        })

    except Exception as e:
        print(f"❌ Reset stats error: {e}")
        return jsonify({'status': 'error', 'message': f'Error: {str(e)}'})

@app.route('/api/trading_test_status')
def trading_test_status():
    """Get detailed status for testing purposes."""
    global trading_engine

    stats = trading_engine.get_performance_stats()

    return jsonify({
        'is_running': trading_engine.is_running,
        'is_live_mode': trading_engine.is_live_mode,
        'current_price': round(trading_engine.current_price, 2),
        'price_source': 'real_api',
        'last_update': datetime.now().isoformat(),
        'performance': stats,
        'test_capabilities': {
            'force_trade': True,
            'close_all': True,
            'test_cycle': True,
            'reset_stats': True
        },
        'model_info': {
            'model_id': trading_engine.model.model_id,
            'composite_score': round(trading_engine.model.composite_score * 100, 1),
            'target_trades_per_day': trading_engine.model.trades_per_day,
            'confidence_threshold': 10.0  # LOWERED from 40.0 to 10.0 for active trading
        }
    })

@app.route('/api/cross_margin_analysis')
def cross_margin_analysis():
    """Get cross margin position sizing analysis."""
    global trading_engine

    current_price = trading_engine.current_price

    # Calculate position sizing for both BUY and SELL
    buy_calc = trading_engine.margin_calculator.calculate_position_size(current_price, 10.0)
    sell_calc = trading_engine.margin_calculator.calculate_position_size(current_price, 10.0)

    # Calculate grid levels
    buy_grids = trading_engine.margin_calculator.calculate_grid_levels(current_price, 'BUY')
    sell_grids = trading_engine.margin_calculator.calculate_grid_levels(current_price, 'SELL')

    return jsonify({
        'current_btc_price': round(current_price, 2),
        'grid_size_percent': trading_engine.model.grid_size_percent * 100,  # Convert to percentage
        'grid_size_usd': round(current_price * trading_engine.model.grid_size_percent, 2),
        'account_config': {
            'account_size': trading_engine.model.account_size,
            'risk_per_trade': trading_engine.model.risk_per_trade,
            'reward_ratio': trading_engine.model.reward_ratio,
            'margin_type': trading_engine.model.margin_type
        },
        'buy_position': {
            'direction': 'BUY',
            'position_size_btc': buy_calc['position_size_btc'],
            'position_size_usd': buy_calc['position_size_usd'],
            'stop_distance_usd': buy_calc['stop_distance_usd'],
            'target_distance_usd': buy_calc['target_distance_usd'],
            'stop_distance_grids': buy_calc['stop_distance_grids'],
            'target_distance_grids': buy_calc['target_distance_grids'],
            'actual_risk': buy_calc['actual_risk'],
            'expected_profit': buy_calc['expected_profit'],
            'stop_loss_price': round(current_price - buy_calc['stop_distance_usd'], 2),
            'take_profit_price': round(current_price + buy_calc['target_distance_usd'], 2)
        },
        'sell_position': {
            'direction': 'SELL',
            'position_size_btc': sell_calc['position_size_btc'],
            'position_size_usd': sell_calc['position_size_usd'],
            'stop_distance_usd': sell_calc['stop_distance_usd'],
            'target_distance_usd': sell_calc['target_distance_usd'],
            'stop_distance_grids': sell_calc['stop_distance_grids'],
            'target_distance_grids': sell_calc['target_distance_grids'],
            'actual_risk': sell_calc['actual_risk'],
            'expected_profit': sell_calc['expected_profit'],
            'stop_loss_price': round(current_price + sell_calc['stop_distance_usd'], 2),
            'take_profit_price': round(current_price - sell_calc['target_distance_usd'], 2)
        },
        'grid_analysis': {
            'buy_grids': buy_grids,
            'sell_grids': sell_grids
        },
        'accuracy_check': {
            'risk_reward_ratio_buy': round(buy_calc['expected_profit'] / buy_calc['actual_risk'], 2),
            'risk_reward_ratio_sell': round(sell_calc['expected_profit'] / sell_calc['actual_risk'], 2),
            'target_ratio': trading_engine.model.reward_ratio,
            'grid_accuracy': 'Positions aligned to 0.25% grid levels'
        }
    })

# AI Signal Monitoring Endpoints
@app.route('/api/ai_status')
def get_ai_monitoring_status():
    """Get current AI signal monitoring status"""
    if not AI_MONITOR_AVAILABLE:
        return jsonify({
            'status': 'error',
            'error': 'AI monitoring not available',
            'ai_monitoring': {'active': False}
        })

    try:
        # Start AI monitoring if not already running
        if not ai_monitor.monitoring:
            ai_monitor.start_monitoring()

        status = ai_monitor.get_current_status()
        trend = ai_monitor.get_confidence_trend(60)  # 60-minute trend
        recent_signals = ai_monitor.get_recent_signals(5)  # Last 5 signals

        return jsonify({
            'status': 'success',
            'ai_monitoring': {
                'active': status['monitoring'],
                'current_confidence': status['current_confidence'],
                'confidence_threshold': status['confidence_threshold'],
                'above_threshold': status['above_threshold'],
                'average_confidence': status.get('average_confidence_20min', 0),
                'last_update': status['last_update'].isoformat() if status.get('last_update') else None,
                'readings_stored': status['readings_stored']
            },
            'signal_activity': {
                'signals_last_hour': status['signals_last_hour'],
                'total_signals': status['total_signals'],
                'recent_signals': recent_signals
            },
            'confidence_trend': {
                'direction': trend['trend'],
                'strength': trend.get('strength', 0),
                'current_avg': trend.get('current_avg', 0),
                'previous_avg': trend.get('previous_avg', 0),
                'time_period': trend.get('time_period', '60 minutes')
            },
            'market_data': {
                'current_price': status.get('current_price', 0),
                'grid_level': status.get('grid_level', 0)
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'ai_monitoring': {'active': False}
        }), 500

def live_trading_loop():
    """Background live trading loop with real BTC data and auto trading."""
    global trading_engine

    try:
        print("🚀 LIVE AUTO TRADING STARTED - REAL BTC DATA ONLY")
        print("=" * 60)
        print("💰 Using Real-Time BTC Price from Binance/Coinbase APIs")
        print("🤖 Bitcoin Freedom Auto Trading Engine Active")
        print("📊 91.0% Composite Score Model Deployed")
        print("⚡ NO DUMMY DATA - LIVE MARKET ONLY")
        print("=" * 60)
        print(f"🔧 DEBUG: Trading loop thread started at {datetime.now().strftime('%H:%M:%S')}")
        print(f"🔧 DEBUG: Engine running status: {trading_engine.is_running}")
        print(f"🔧 DEBUG: Thread name: {threading.current_thread().name}")
        print(f"🔧 DEBUG: Thread is daemon: {threading.current_thread().daemon}")

        update_counter = 0

        # Verify the loop is actually running
        print("🔧 DEBUG: Entering main trading loop...")

    except Exception as e:
        print(f"❌ CRITICAL: Error in trading loop initialization: {e}")
        return

    while trading_engine.is_running:
        try:
            # Update real market price every cycle
            trading_engine.update_market_price()

            # Check for trade exits first
            trading_engine.check_trade_exits()

            # Auto trading logic - ACTIVE TRADING (NO ARTIFICIAL LIMITS)
            if trading_engine.should_enter_trade():
                direction, confidence = trading_engine.generate_trade_signal()
                if direction and confidence > 0.1:  # LOWERED confidence threshold for more trades
                    print(f"🎯 AUTO TRADE SIGNAL: {direction} | Confidence: {confidence:.1%}")
                    trading_engine.enter_trade(direction, confidence)

            # Print status every 30 seconds (15 cycles)
            update_counter += 1

            # Debug message every 5 cycles (10 seconds) to verify loop is running
            if update_counter % 5 == 0:
                print(f"🔧 DEBUG: Loop cycle #{update_counter} at {datetime.now().strftime('%H:%M:%S')}")

            if update_counter % 15 == 0:
                stats = trading_engine.get_performance_stats()
                print(f"\n📊 AUTO TRADING STATUS - {datetime.now().strftime('%H:%M:%S')}")
                print(f"   💰 BTC Price: ${trading_engine.current_price:,.2f} (REAL)")
                print(f"   💼 Equity: ${stats['equity']:,.2f} | P&L: ${stats['total_profit']:+,.2f}")
                print(f"   📈 Open: {stats['open_positions']} | Daily: {stats['daily_trades']} trades")
                print(f"   🎯 Win Rate: {stats['win_rate']}% | Daily P&L: ${stats['daily_pnl']:+.2f}")

            # Sleep for 2 seconds (real-time updates)
            time.sleep(2)

        except Exception as e:
            print(f"❌ Live trading loop error: {e}")
            time.sleep(5)

    print("🛑 LIVE AUTO TRADING STOPPED")

if __name__ == '__main__':
    # Create templates directory
    os.makedirs('templates', exist_ok=True)

    # Get real BTC price for startup
    price_fetcher = RealTimePriceFetcher()
    current_btc_price = price_fetcher.get_real_btc_price()

    print("🚀 BITCOIN FREEDOM LIVE AUTO TRADING WEB APPLICATION")
    print("=" * 70)
    print(f"💰 REAL BTC PRICE: ${current_btc_price:,.2f} (LIVE DATA)")
    print(f"🏆 Best Composite Model: {BestCompositeModel().model_id}")
    print(f"📊 Composite Score: {BestCompositeModel().composite_score:.1%}")
    print(f"💰 Risk per Trade: ${BestCompositeModel().risk_per_trade}")
    print(f"🎯 Target Win Rate: {BestCompositeModel().win_rate:.1%}")
    print(f"📈 Target Trades/Day: {BestCompositeModel().trades_per_day}")
    print(f"💼 Account Size: ${BestCompositeModel().account_size}")
    print(f"⚡ Live Trading Ready: {BestCompositeModel().live_trading_ready}")
    print("=" * 70)
    print("🤖 AUTO TRADING FEATURES:")
    print("   ✅ Real-time BTC price from Binance/Coinbase APIs")
    print("   ✅ NO dummy data - live market only")
    print("   ✅ Bitcoin Freedom ensemble auto trading")
    print("   ✅ ONLY 4 indicators: VWAP(20), RSI(5), BB Position, ETH/BTC Ratio")
    print("   ✅ Latest TCN-CNN-PPO retraining: 79.1% composite, 93.2% win rate")
    print("   ✅ Conservative risk management")
    print("   ✅ $10 risk / $25 profit per trade")
    print("   ✅ Ready for Binance integration")
    print("=" * 70)
    print("🌐 Starting web server on http://localhost:5000")
    print("⚠️  LIVE AUTO TRADING - Monitor closely!")

    # Start AI monitoring if available
    if AI_MONITOR_AVAILABLE and ai_monitor:
        try:
            ai_monitor.start_monitoring()
            print("🤖 AI Signal Monitor started - confidence tracking active")
        except Exception as e:
            print(f"⚠️ AI Monitor startup failed: {e}")

    app.run(debug=False, host='0.0.0.0', port=5000)
