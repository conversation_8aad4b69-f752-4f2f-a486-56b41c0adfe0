<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>🚀 Bitcoin Freedom Production Trading Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2d3748 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(0,0,0,0.3));
            padding: 30px;
            text-align: center;
            border-bottom: 3px solid #00d4aa;
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.4);
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,212,170,0.1), rgba(74,222,128,0.1), rgba(34,211,238,0.1));
            z-index: -1;
        }
        
        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #00d4aa, #4ade80, #22d3ee);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            letter-spacing: -1px;
            text-shadow: 0 0 40px rgba(0,212,170,0.4);
        }
        
        .header .subtitle {
            font-size: 1.4em;
            color: #94a3b8;
            margin-bottom: 25px;
            font-weight: 400;
        }
        
        .status-bar {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1em;
            backdrop-filter: blur(10px);
            border: 2px solid;
            transition: all 0.3s ease;
        }
        
        .status-running {
            background: rgba(0,212,170,0.2);
            border-color: #00d4aa;
            color: #00d4aa;
        }
        
        .status-stopped {
            background: rgba(239,68,68,0.2);
            border-color: #ef4444;
            color: #ef4444;
        }
        
        .status-live {
            background: rgba(251,146,60,0.2);
            border-color: #fb923c;
            color: #fb923c;
        }
        
        .status-sim {
            background: rgba(59,130,246,0.2);
            border-color: #3b82f6;
            color: #3b82f6;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 30px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-start {
            background: linear-gradient(135deg, #00d4aa, #4ade80);
            color: white;
            box-shadow: 0 8px 25px rgba(0,212,170,0.3);
        }
        
        .btn-start:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,212,170,0.4);
        }
        
        .btn-stop {
            background: linear-gradient(135deg, #ef4444, #f87171);
            color: white;
            box-shadow: 0 8px 25px rgba(239,68,68,0.3);
        }
        
        .btn-stop:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(239,68,68,0.4);
        }
        
        .btn-toggle {
            background: linear-gradient(135deg, #fb923c, #fdba74);
            color: white;
            box-shadow: 0 8px 25px rgba(251,146,60,0.3);
        }
        
        .btn-toggle:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(251,146,60,0.4);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }
        
        .price-display {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .price-value {
            font-size: 3.5em;
            font-weight: 800;
            background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(251,191,36,0.3);
            margin-bottom: 10px;
        }
        
        .price-label {
            font-size: 1.2em;
            color: #94a3b8;
            font-weight: 500;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .card {
            background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 24px;
            padding: 35px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00d4aa, #4ade80, #22d3ee);
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.4);
        }
        
        .card h3 {
            font-size: 1.8em;
            font-weight: 700;
            color: #00d4aa;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .card h3 i {
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .metric {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 16px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        
        .metric:hover {
            background: rgba(255,255,255,0.1);
            transform: scale(1.05);
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #94a3b8;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 1.8em;
            font-weight: 700;
            color: #e2e8f0;
        }
        
        .profit { color: #00d4aa; }
        .loss { color: #ef4444; }
        .running { color: #00d4aa; }
        .stopped { color: #ef4444; }

        /* Trades Table Styles */
        .trades-container {
            margin-top: 20px;
        }

        .trades-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .trade-controls {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            font-size: 0.9em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-small:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79,70,229,0.3);
        }

        .trades-table-container {
            overflow-x: auto;
            border-radius: 12px;
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .trades-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }

        .trades-table th {
            background: linear-gradient(135deg, rgba(0,212,170,0.2), rgba(0,212,170,0.1));
            color: #00d4aa;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid rgba(0,212,170,0.3);
        }

        .trades-table td {
            padding: 12px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            transition: background 0.3s ease;
        }

        .trades-table tr:hover td {
            background: rgba(255,255,255,0.05);
        }

        .trades-table .no-trades {
            text-align: center;
            color: #94a3b8;
            font-style: italic;
            padding: 30px;
        }

        .trade-direction-buy {
            color: #00d4aa;
            font-weight: 600;
        }

        .trade-direction-sell {
            color: #ef4444;
            font-weight: 600;
        }

        .trade-pnl-positive {
            color: #00d4aa;
            font-weight: 600;
        }

        .trade-pnl-negative {
            color: #ef4444;
            font-weight: 600;
        }

        .trade-status {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .trade-status-closed {
            background: rgba(0,212,170,0.2);
            color: #00d4aa;
        }

        .trade-status-open {
            background: rgba(251,146,60,0.2);
            color: #fb923c;
        }

        .trade-id {
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            color: #94a3b8;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2.5em; }
            .price-value { font-size: 2.5em; }
            .dashboard-grid { grid-template-columns: 1fr; }
            .controls { flex-direction: column; align-items: center; }
            .status-bar { flex-direction: column; }

            .trades-table-container {
                font-size: 0.8em;
            }

            .trades-table th,
            .trades-table td {
                padding: 8px 6px;
            }

            .trade-controls {
                flex-direction: column;
                width: 100%;
            }

            .btn-small {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-rocket"></i> Bitcoin Freedom Live Trading</h1>
        <div class="subtitle" id="headerSubtitle">Production-Ready Auto Trading System | Loading...</div>
        
        <div class="status-bar">
            <div class="status-indicator status-stopped" id="tradingStatus">
                <i class="fas fa-circle"></i>
                <span>Trading Stopped</span>
            </div>
            <div class="status-indicator status-sim" id="modeStatus">
                <i class="fas fa-flask"></i>
                <span>Simulation Mode</span>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Debug Section - Temporary -->
        <div id="debugSection" style="background: rgba(255,0,0,0.1); padding: 20px; margin: 20px 0; border-radius: 10px; border: 2px solid red;">
            <h3 style="color: #ff6b6b;">🔧 DEBUG INFO (Temporary)</h3>
            <div id="debugInfo" style="font-family: monospace; color: #fff; margin-top: 10px;">
                Waiting for data...
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                <i class="fas fa-play"></i> Start Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                <i class="fas fa-stop"></i> Stop Trading
            </button>
            <button class="btn btn-toggle" id="toggleBtn" onclick="toggleMode()">
                <i class="fas fa-exchange-alt"></i> Switch to Live Mode
            </button>
        </div>
        
        <div class="price-display">
            <div class="price-value" id="currentPrice">$105,000</div>
            <div class="price-label">Bitcoin Price (Real-Time)</div>
        </div>
        
        <div class="dashboard-grid">
            <div class="card">
                <h3><i class="fas fa-chart-line"></i> Trading Performance</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-label">Equity</div>
                        <div class="metric-value" id="equity">$300.00</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value" id="totalPnl">$0.00</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value" id="winRate">--%</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Daily P&L</div>
                        <div class="metric-value" id="dailyPnl">$0.00</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3><i class="fas fa-cogs"></i> System Status</h3>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-label">Open Positions</div>
                        <div class="metric-value" id="openPositions">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Daily Trades</div>
                        <div class="metric-value" id="dailyTrades">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Total Trades</div>
                        <div class="metric-value" id="totalTrades">0</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Model Score</div>
                        <div class="metric-value" id="compositeScore">--%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Trades Section -->
        <div class="card" style="grid-column: 1 / -1; margin-top: 20px;">
            <h3><i class="fas fa-history"></i> Recent Trades</h3>
            <div class="trades-container">
                <div class="trades-header">
                    <div class="trade-controls">
                        <button class="btn-small" onclick="refreshTrades()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button class="btn-small" onclick="clearTrades()">
                            <i class="fas fa-trash"></i> Clear History
                        </button>
                    </div>
                </div>
                <div class="trades-table-container">
                    <table class="trades-table" id="tradesTable">
                        <thead>
                            <tr>
                                <th>Trade ID</th>
                                <th>Direction</th>
                                <th>Entry Time</th>
                                <th>Exit Time</th>
                                <th>Entry Price</th>
                                <th>Exit Price</th>
                                <th>Position (BTC)</th>
                                <th>Position (USD)</th>
                                <th>Duration</th>
                                <th>P&L</th>
                                <th>P&L %</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="tradesTableBody">
                            <tr>
                                <td colspan="12" class="no-trades">No trades yet - start trading to see results</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRunning = false;
        let isLiveMode = false;
        let dashboardInterval = null;
        
        function startTrading() {
            fetch('/api/start_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isRunning = true;
                        updateButtons();
                        updateStatus();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
        }
        
        function stopTrading() {
            fetch('/api/stop_trading', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isRunning = false;
                        updateButtons();
                        updateStatus();
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
        }
        
        function toggleMode() {
            const newMode = !isLiveMode;

            if (newMode) {
                // Show options for live mode
                const choice = prompt(
                    '⚠️ LIVE TRADING OPTIONS:\n\n' +
                    '1. TESTNET SPOT (Safe testing with fake money)\n' +
                    '2. LIVE SPOT (Real spot trading)\n' +
                    '3. LIVE CROSS MARGIN (Real margin trading)\n\n' +
                    'Enter 1, 2, or 3:'
                );

                if (choice === null) return; // User cancelled

                let testnet = true;
                let useMargin = false;
                let confirmMessage = '';

                if (choice === '3') {
                    testnet = false;
                    useMargin = true;
                    confirmMessage = '🚨 CROSS MARGIN TRADING!\n\n' +
                                   'This will use REAL MONEY with LEVERAGE.\n' +
                                   'You can lose more than your initial investment.\n' +
                                   'Current margin level: Check your account first.\n\n' +
                                   'Are you absolutely sure you want to continue?';
                } else if (choice === '2') {
                    testnet = false;
                    useMargin = false;
                    confirmMessage = '🚨 LIVE SPOT TRADING!\n\n' +
                                   'This will place actual orders on Binance with your funds.\n' +
                                   'No leverage, but real money at risk.\n\n' +
                                   'Are you absolutely sure you want to continue?';
                } else if (choice === '1') {
                    testnet = true;
                    useMargin = false;
                    confirmMessage = '✅ TESTNET MODE: Safe testing with fake money.\n\n' +
                                   'No real money will be used. Continue?';
                } else {
                    alert('Invalid choice. Please enter 1, 2, or 3.');
                    return;
                }

                if (!confirm(confirmMessage)) return;

                fetch('/api/toggle_live_mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        live_mode: newMode,
                        testnet: testnet,
                        use_margin: useMargin
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isLiveMode = data.live_mode;
                        updateButtons();
                        updateStatus();
                        alert(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            } else {
                // Switching to simulation mode
                fetch('/api/toggle_live_mode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ live_mode: false })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        isLiveMode = data.live_mode;
                        updateButtons();
                        updateStatus();
                        alert(data.message);
                    } else {
                        alert('Error: ' + data.message);
                    }
                });
            }
        }
        
        function updateDashboard() {
            console.log('Fetching dashboard data...');

            fetch('/api/trading_status')
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Dashboard update data:', data); // Debug log

                    // Update debug section with visible info
                    const debugInfo = document.getElementById('debugInfo');
                    if (debugInfo) {
                        debugInfo.innerHTML = `
                            <strong>✅ API Response Received:</strong><br>
                            Model ID: ${data.model_info.model_id}<br>
                            Model Type: ${data.model_info.model_type}<br>
                            Composite Score: ${data.model_info.composite_score}%<br>
                            Win Rate: ${data.model_info.win_rate_target}%<br>
                            Current Price: $${data.current_price.toLocaleString()}<br>
                            Last Update: ${new Date().toLocaleTimeString()}
                        `;
                    }

                    isRunning = data.is_running;
                    isLiveMode = data.is_live_mode;

                    document.getElementById('currentPrice').textContent = '$' + data.current_price.toLocaleString();
                    document.getElementById('equity').textContent = '$' + data.performance.equity.toFixed(2);
                    document.getElementById('totalPnl').textContent = '$' + data.performance.total_profit.toFixed(2);
                    document.getElementById('winRate').textContent = data.performance.win_rate + '%';
                    document.getElementById('dailyPnl').textContent = '$' + data.performance.daily_pnl.toFixed(2);
                    document.getElementById('openPositions').textContent = data.performance.open_positions;
                    document.getElementById('dailyTrades').textContent = data.performance.daily_trades;
                    document.getElementById('totalTrades').textContent = data.performance.total_trades;
                    document.getElementById('compositeScore').textContent = data.model_info.composite_score + '%';

                    // Update header subtitle with real model information
                    const headerSubtitle = `Production-Ready Auto Trading System | ${data.model_info.composite_score}% Composite Score | ${data.model_info.model_type}`;
                    document.getElementById('headerSubtitle').textContent = headerSubtitle;
                    console.log('Updated header subtitle:', headerSubtitle); // Debug log

                    updateButtons();
                    updateStatus();
                    updateColors();
                })
                .catch(error => {
                    console.error('Error updating dashboard:', error);

                    // Update debug section with error info
                    const debugInfo = document.getElementById('debugInfo');
                    if (debugInfo) {
                        debugInfo.innerHTML = `
                            <strong>❌ API Error:</strong><br>
                            Error: ${error.message}<br>
                            Time: ${new Date().toLocaleTimeString()}<br>
                            <em>Check console for details</em>
                        `;
                    }
                });

            // Update recent trades
            updateRecentTrades();
        }

        function updateRecentTrades() {
            fetch('/api/recent_trades')
                .then(response => response.json())
                .then(trades => {
                    const tbody = document.getElementById('tradesTableBody');

                    if (trades.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="12" class="no-trades">No trades yet - start trading to see results</td></tr>';
                        return;
                    }

                    tbody.innerHTML = trades.slice(-10).reverse().map(trade => {
                        const directionClass = trade.direction === 'BUY' ? 'trade-direction-buy' : 'trade-direction-sell';
                        const pnlClass = trade.pnl >= 0 ? 'trade-pnl-positive' : 'trade-pnl-negative';
                        const statusClass = trade.status.includes('CLOSED') ? 'trade-status-closed' : 'trade-status-open';

                        return `
                            <tr>
                                <td class="trade-id">${trade.trade_id.split('_').slice(-2).join('_')}</td>
                                <td class="${directionClass}">${trade.direction}</td>
                                <td>${trade.entry_time}</td>
                                <td>${trade.exit_time || 'Open'}</td>
                                <td>$${trade.entry_price.toLocaleString()}</td>
                                <td>${trade.exit_price ? '$' + trade.exit_price.toLocaleString() : 'Open'}</td>
                                <td>${trade.position_size}</td>
                                <td>$${trade.position_usd.toLocaleString()}</td>
                                <td>${trade.duration}</td>
                                <td class="${pnlClass}">$${trade.pnl.toFixed(2)}</td>
                                <td class="${pnlClass}">${trade.profit_percentage.toFixed(2)}%</td>
                                <td><span class="trade-status ${statusClass}">${trade.status.replace('CLOSED_', '')}</span></td>
                            </tr>
                        `;
                    }).join('');
                })
                .catch(error => {
                    console.error('Error fetching trades:', error);
                });
        }

        function refreshTrades() {
            updateRecentTrades();
        }

        function clearTrades() {
            if (confirm('Clear all trade history? This action cannot be undone.')) {
                fetch('/api/reset_trading_stats', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            updateDashboard();
                            updateRecentTrades();  // Refresh trades table
                            alert('Trade history cleared successfully');
                        } else {
                            alert('Error clearing trades: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error clearing trades:', error);
                        alert('Error clearing trades: ' + error.message);
                    });
            }
        }
        
        function updateButtons() {
            document.getElementById('startBtn').disabled = isRunning;
            document.getElementById('stopBtn').disabled = !isRunning;
            document.getElementById('toggleBtn').disabled = isRunning;
            document.getElementById('toggleBtn').innerHTML = isLiveMode ? 
                '<i class="fas fa-exchange-alt"></i> Switch to Simulation' : 
                '<i class="fas fa-exchange-alt"></i> Switch to Live Mode';
        }
        
        function updateStatus() {
            const tradingStatus = document.getElementById('tradingStatus');
            const modeStatus = document.getElementById('modeStatus');
            
            if (isRunning) {
                tradingStatus.className = 'status-indicator status-running pulse';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Active</span>';
            } else {
                tradingStatus.className = 'status-indicator status-stopped';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Stopped</span>';
            }
            
            if (isLiveMode) {
                modeStatus.className = 'status-indicator status-live pulse';
                modeStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>LIVE MODE</span>';
            } else {
                modeStatus.className = 'status-indicator status-sim';
                modeStatus.innerHTML = '<i class="fas fa-flask"></i><span>Simulation Mode</span>';
            }
        }
        
        function updateColors() {
            const totalPnl = parseFloat(document.getElementById('totalPnl').textContent.replace('$', ''));
            const dailyPnl = parseFloat(document.getElementById('dailyPnl').textContent.replace('$', ''));

            document.getElementById('totalPnl').className = 'metric-value ' + (totalPnl >= 0 ? 'profit' : 'loss');
            document.getElementById('dailyPnl').className = 'metric-value ' + (dailyPnl >= 0 ? 'profit' : 'loss');
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded - initializing dashboard...');

            // Clear any existing interval
            if (dashboardInterval) {
                clearInterval(dashboardInterval);
            }

            // Start dashboard updates
            updateDashboard(); // Initial load
            dashboardInterval = setInterval(updateDashboard, 2000); // Update every 2 seconds

            console.log('Dashboard interval started');
        });
    </script>
</body>
</html>
